name: tubewell_water_billing
description: "Professional water billing management system for tubewell operators with multi-account support, advanced reporting, PDF generation, and comprehensive financial tracking."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.6.1
dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8

  # State Management
  provider: ^6.1.2

  # Database
  sqflite: ^2.3.3+1

  # Local Storage & Preferences
  shared_preferences: ^2.2.3

  # File & Path Operations
  path: ^1.9.0
  path_provider: ^2.1.3
  file_picker: ^8.0.6

  # PDF Generation
  pdf: ^3.11.0

  # Image Processing
  flutter_image_compress: ^2.3.0

  # Permissions
  permission_handler: ^11.3.1

  # Charts & Data Visualization
  fl_chart: ^0.68.0

  # Utilities
  intl: ^0.19.0
  logger: ^2.3.0
  uuid: ^4.4.0
  synchronized: ^3.1.0+1

  # File Operations & Sharing
  open_file: ^3.3.2
  open_file_macos: ^0.1.0
  share_plus: ^9.0.0

  # UI Enhancements
  flutter_colorpicker: ^1.1.0

  # Background Tasks
  workmanager: ^0.5.2

  # URL Launcher
  url_launcher: ^6.2.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.4.0
flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/icons/

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#5DADE2"
    theme_color: "#5DADE2"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"

# Flutter Native Splash Configuration
flutter_native_splash:
  color: "#5DADE2"
  image: "assets/icons/app_icon.png"
  branding: "assets/icons/app_icon.png"
  color_dark: "#2C3E50"
  image_dark: "assets/icons/app_icon.png"
  branding_dark: "assets/icons/app_icon.png"
  android_12:
    image: "assets/icons/app_icon.png"
    icon_background_color: "#5DADE2"
    image_dark: "assets/icons/app_icon.png"
    icon_background_color_dark: "#2C3E50"
  web: false
