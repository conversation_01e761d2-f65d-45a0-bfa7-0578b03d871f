import 'dart:io';
import 'package:open_file/open_file.dart';
import 'package:logger/logger.dart';

/// Service for handling file-related operations
class FileService {
  static final Logger _logger = Logger();

  /// Opens the directory containing the file at the given path
  ///
  /// This will open the file explorer/finder at the directory location
  /// Returns true if successful, false otherwise
  static Future<bool> openFileLocation(String filePath) async {
    try {
      _logger.i('Attempting to open file location: $filePath');

      final file = File(filePath);
      if (!await file.exists()) {
        _logger.e('File does not exist: $filePath');
        return false;
      }

      // Get the directory path
      final directoryPath = file.parent.path;
      _logger.i('Directory path: $directoryPath');

      // Special handling for Downloads folder on Android
      if (Platform.isAndroid) {
        // If path contains Download or tubewell_backups, try to open the Downloads folder directly
        if (directoryPath.contains('/Download') || directoryPath.contains('tubewell_backups')) {
          _logger.i('Detected Downloads folder, trying direct approach');

          // Try multiple approaches to open the Downloads folder

          // First try: Use intent to open Downloads folder with file manager
          try {
            // Try to use the system file manager to open the Downloads folder
            final intent = 'content://com.android.externalstorage.documents/document/primary%3ADownload';
            final intentResult = await OpenFile.open(intent);

            if (intentResult.type == ResultType.done) {
              _logger.i('Successfully opened Downloads folder with content URI intent');
              return true;
            }

            _logger.w('Failed to open Downloads with content URI intent: ${intentResult.message}');
          } catch (e) {
            _logger.w('Error trying content URI intent approach: $e');
          }

          // Second try: Use the standard Downloads path
          final downloadsResult = await OpenFile.open(
            '/storage/emulated/0/Download',
            type: "resource/folder",
          );

          if (downloadsResult.type == ResultType.done) {
            _logger.i('Successfully opened Downloads folder with standard path');
            return true;
          }

          _logger.w('Failed to open Downloads with standard path: ${downloadsResult.message}');

          // Third try: Use the file's parent directory
          final directResult = await OpenFile.open(
            directoryPath,
          );

          if (directResult.type == ResultType.done) {
            _logger.i('Successfully opened directory directly');
            return true;
          }

          _logger.w('Failed to open directory directly: ${directResult.message}');

          // Fourth try: Use Android's content URI for Downloads
          try {
            final contentResult = await OpenFile.open(
              'content://com.android.providers.downloads.documents/document/downloads',
            );

            if (contentResult.type == ResultType.done) {
              _logger.i('Successfully opened Downloads with content URI');
              return true;
            }

            _logger.w('Failed to open Downloads with content URI: ${contentResult.message}');
          } catch (e) {
            _logger.w('Error trying content URI approach: $e');
          }
        }
      }

      // On Android, we can open the directory directly
      if (Platform.isAndroid) {
        _logger.i('Trying standard Android folder opening approaches');

        // Try multiple MIME types for folders
        final mimeTypes = [
          "resource/folder",
          "application/x-directory",
          "inode/directory",
          "*/*"  // Last resort - any type
        ];

        // Try each MIME type
        for (final mimeType in mimeTypes) {
          _logger.i('Trying to open directory with MIME type: $mimeType');

          final result = await OpenFile.open(
            directoryPath,
            type: mimeType,
          );

          if (result.type == ResultType.done) {
            _logger.i('Successfully opened directory with MIME type: $mimeType');
            return true;
          }

          _logger.w('Failed with MIME type $mimeType: ${result.message}');
        }

        // If all MIME types fail, try using an intent to view the directory
        try {
          _logger.i('Trying to use file manager intent');

          // Try opening the parent directory as a last resort
          final parentDir = Directory(directoryPath).parent.path;
          final parentResult = await OpenFile.open(
            parentDir,
          );

          if (parentResult.type == ResultType.done) {
            _logger.i('Successfully opened parent directory');
            return true;
          }

          _logger.w('Failed to open parent directory: ${parentResult.message}');
        } catch (e) {
          _logger.e('Error trying to open parent directory: $e');
        }

        // If we get here, all attempts have failed
        _logger.e('All attempts to open directory failed');
        return false;
      }
      // On iOS, we should try to open the directory instead of the file
      else if (Platform.isIOS) {
        final result = await OpenFile.open(
          directoryPath,
          type: "public.folder", // Use UTI for folders on iOS
        );
        if (result.type != ResultType.done) {
          _logger.e('Could not open directory: $directoryPath, error: ${result.message}');

          // If opening the directory fails, try opening the parent directory
          final parentDir = Directory(directoryPath).parent.path;
          final parentResult = await OpenFile.open(
            parentDir,
            type: "public.folder",
          );
          if (parentResult.type != ResultType.done) {
            _logger.e('Could not open parent directory: $parentDir, error: ${parentResult.message}');
            return false;
          }
          return true;
        }
        return true;
      }
      // On desktop platforms, we can try to open the directory
      else {
        // For Windows/macOS/Linux
        String? folderType;

        if (Platform.isWindows) {
          folderType = "application/x-directory";
        } else if (Platform.isMacOS) {
          folderType = "public.folder";
        } else {
          folderType = "inode/directory";
        }

        final result = await OpenFile.open(
          directoryPath,
          type: folderType,
        );

        if (result.type != ResultType.done) {
          _logger.e(
              'Could not open directory: $directoryPath, error: ${result.message}');

          // If opening the directory fails, try opening the parent directory
          final parentDir = Directory(directoryPath).parent.path;
          final parentResult = await OpenFile.open(
            parentDir,
            type: folderType,
          );

          if (parentResult.type != ResultType.done) {
            _logger.e('Could not open parent directory: $parentDir, error: ${parentResult.message}');
            return false;
          }
          return true;
        }
        return true;
      }
    } catch (e) {
      _logger.e('Error opening file location: $e');
      return false;
    }
  }
}
