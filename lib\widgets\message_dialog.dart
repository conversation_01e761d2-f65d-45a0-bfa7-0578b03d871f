import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/message_service.dart';

/// A dialog that shows options to send WhatsApp or SMS messages after a transaction
class MessageDialog extends StatefulWidget {
  final Customer customer;
  final Bill? bill;
  final Payment? payment;
  final double? remainingCredit;
  final String title;
  final String? customMessage;

  const MessageDialog({
    super.key,
    required this.customer,
    this.bill,
    this.payment,
    this.remainingCredit,
    required this.title,
    this.customMessage,
  }) : assert(bill != null || payment != null || customMessage != null,
            'Either bill, payment, or customMessage must be provided');

  @override
  State<MessageDialog> createState() => _MessageDialogState();
}

class _MessageDialogState extends State<MessageDialog> {
  String _message = 'Loading message...';
  bool _isSending = false;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeMessage();
  }

  Future<void> _initializeMessage() async {
    try {
      if (widget.customMessage != null) {
        _message = widget.customMessage!;
      } else if (widget.bill != null) {
        _message = await MessageService.formatBillMessage(
            widget.bill!, widget.customer);
      } else if (widget.payment != null) {
        _message = await MessageService.formatPaymentMessage(
          widget.payment!,
          widget.customer,
          remainingCredit: widget.remainingCredit,
        );
      } else {
        _message = 'No message content available.';
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _message = 'Error loading message: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sendWhatsApp() async {
    if (widget.customer.contactNumber == null ||
        widget.customer.contactNumber!.isEmpty) {
      setState(() {
        _errorMessage = 'Customer does not have a contact number.';
      });
      return;
    }

    setState(() {
      _isSending = true;
      _errorMessage = null;
    });

    try {
      final success = await MessageService.sendWhatsAppMessage(
        widget.customer.contactNumber!,
        _message,
      );

      if (!success && mounted) {
        setState(() {
          _errorMessage =
              'Failed to open WhatsApp. Make sure WhatsApp is installed.';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  Future<void> _sendSms() async {
    if (widget.customer.contactNumber == null ||
        widget.customer.contactNumber!.isEmpty) {
      setState(() {
        _errorMessage = 'Customer does not have a contact number.';
      });
      return;
    }

    setState(() {
      _isSending = true;
      _errorMessage = null;
    });

    try {
      final success = await MessageService.sendSmsMessage(
        widget.customer.contactNumber!,
        _message,
      );

      if (!success && mounted) {
        setState(() {
          _errorMessage = 'Failed to open SMS app.';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.customer.contactNumber != null &&
                widget.customer.contactNumber!.isNotEmpty)
              Text(
                'Contact: ${widget.customer.contactNumber}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            const SizedBox(height: 16),
            const Text('Message Preview:'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Text(_message),
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        if (widget.customer.contactNumber != null &&
            widget.customer.contactNumber!.isNotEmpty) ...[
          ElevatedButton.icon(
            onPressed: _isSending ? null : _sendSms,
            icon: const Icon(Icons.sms),
            label: const Text('SMS'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: _isSending ? null : _sendWhatsApp,
            icon: const Icon(Icons.message),
            label: const Text('WhatsApp'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF25D366), // WhatsApp green
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ],
    );
  }
}
