# 💧 Tubewell Water Billing App

**Professional water billing management system for tubewell operators with multi-account support, advanced reporting, PDF generation, and comprehensive financial tracking.**

[![Flutter](https://img.shields.io/badge/Flutter-3.6.1-blue.svg)](https://flutter.dev/)
[![Version](https://img.shields.io/badge/Version-1.0.0-green.svg)](https://github.com/your-repo/tubewell-water-billing)
[![License](https://img.shields.io/badge/License-All%20Rights%20Reserved-red.svg)](LICENSE)

## 🌟 Overview

The Tubewell Water Billing App is a comprehensive, professional-grade solution designed specifically for tubewell operators and water billing businesses. It combines powerful billing capabilities with advanced financial management, customer relationship tools, and detailed reporting features.

## ✨ Key Features

### 💧 **Water Billing Management**
- **Time-Based Billing**: Accurate billing based on start/end times with automatic duration calculation
- **Hourly Rate System**: Customizable hourly rates with automatic amount calculation
- **Discount Management**: Apply time-based and amount-based discounts
- **Bill Editing**: Complete bill modification with customer selection
- **Payment Status Tracking**: Paid, unpaid, and partial payment support

### 👥 **Customer Management**
- **Complete Customer Database**: Store customer information with contact details
- **Customer Profiles**: Detailed customer pages with billing history
- **Balance Tracking**: Real-time customer balance calculations
- **Credit System**: Customer credit management with automatic application
- **Search & Filter**: Advanced customer search and filtering options

### 💳 **Payment Processing**
- **Multiple Payment Methods**: Cash, Bank Transfer, UPI, Check, and custom methods
- **Partial Payments**: Support for partial bill payments
- **Payment History**: Complete payment tracking and history
- **Auto Payment Application**: Automatic credit application to bills
- **Payment Reminders**: Automated reminder system for unpaid bills

### 🏢 **Multi-Account System**
- **Account Isolation**: Complete data separation between different accounts
- **Easy Account Switching**: Quick switching between business accounts
- **Account Management**: Create, edit, and manage multiple business profiles
- **Independent Settings**: Separate currency and settings per account
- **Data Security**: Secure account-based data storage

### 📊 **Financial Analytics & Reporting**
- **Real-Time Dashboard**: Live financial overview with interactive charts
- **Expense Tracking**: Categorized expense management with detailed analytics
- **Income Analysis**: Comprehensive income tracking and reporting
- **Payment Method Analytics**: Detailed breakdown by payment methods
- **Date Range Filtering**: Custom date range analysis and reporting
- **Export Capabilities**: PDF export for all major reports

### 📄 **Professional PDF Generation**
- **Customizable Templates**: Multiple PDF template options
- **Bill Generation**: Professional bill PDFs with company branding
- **Summary Reports**: Comprehensive financial summary PDFs
- **Customer Reports**: Individual customer statement generation
- **Expense Reports**: Detailed expense category reports
- **Bulk Export**: Export multiple records in single PDF

### 📱 **Communication Features**
- **WhatsApp Integration**: Send bills and reminders via WhatsApp
- **SMS Support**: SMS bill delivery and payment reminders
- **Custom Templates**: Customizable message templates for different scenarios
- **Bulk Messaging**: Send reminders to multiple customers
- **Message History**: Track sent messages and delivery status

### 🔧 **Advanced Features**
- **Smart Search**: Advanced search across all data types
- **Multiple Sorting**: Various sorting options for all lists
- **Data Backup**: Local and cloud backup with scheduled automation
- **Data Restore**: Easy restore from backup files
- **Currency Support**: Multi-currency support with regional settings
- **Dark/Light Theme**: Automatic theme switching based on system preferences

## 🏗️ Technical Architecture

### **Frontend**
- **Framework**: Flutter 3.6.1 with Dart
- **UI Design**: Material Design 3 with custom theming
- **State Management**: Provider pattern for efficient state handling
- **Navigation**: Bottom navigation with smooth transitions
- **Responsive Design**: Optimized for different screen sizes

### **Backend & Storage**
- **Database**: SQLite for robust local data storage
- **Data Models**: Comprehensive data models for bills, customers, payments, expenses
- **Services**: Modular service architecture for different functionalities
- **Backup System**: Automated backup with multiple storage options

### **Key Dependencies**
- `sqflite`: Local SQLite database management
- `provider`: State management solution
- `pdf`: Professional PDF generation
- `fl_chart`: Interactive charts and analytics
- `intl`: Internationalization and date formatting
- `url_launcher`: WhatsApp and SMS integration
- `file_picker`: File operations and backup/restore
- `shared_preferences`: Settings and preferences storage

## 📱 App Screens

### **Main Navigation (5 Screens)**
1. **🏠 Dashboard/Summary**: Real-time financial overview with charts
2. **📄 Bills/Transactions**: Complete bill management with advanced filtering
3. **👥 Customers**: Customer database with detailed profiles
4. **💳 Payments**: Payment tracking with multiple methods
5. **📊 Expenses**: Expense management with category analytics

### **Additional Screens**
- **Customer Detail**: Individual customer profiles with 3 tabs (Summary, Transactions, Payments)
- **Bill Details**: Comprehensive bill information with edit capabilities
- **Settings**: Account management, currency settings, PDF templates, message templates
- **Backup & Restore**: Data backup and restore functionality
- **App Information**: Comprehensive app details and feature overview

## 🚀 Installation & Setup

### **Prerequisites**
- Flutter SDK 3.6.1 or higher
- Dart SDK
- Android Studio / VS Code
- Android SDK (for Android builds)
- Xcode (for iOS builds - macOS only)

### **Getting Started**

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-repo/tubewell-water-billing.git
   cd tubewell-water-billing
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate app icons**
   ```bash
   flutter pub run flutter_launcher_icons
   ```

4. **Generate splash screen**
   ```bash
   flutter pub run flutter_native_splash:create
   ```

5. **Run the app**
   ```bash
   flutter run
   ```

### **Building for Production**

#### **Android APK (Optimized)**
```bash
# Use the provided build script for optimized APK
./build_apk.sh  # Linux/Mac
build_apk.bat   # Windows

# Or manual build
flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info
```

#### **Android App Bundle (Play Store)**
```bash
flutter build appbundle --release --shrink --obfuscate --split-debug-info=build/debug-info
```

#### **iOS**
```bash
flutter build ios --release
```

## 📋 Usage Guide

### **First Time Setup**
1. **Create Account**: Set up your first business account
2. **Currency Settings**: Configure your preferred currency
3. **Add Customers**: Import or manually add customer information
4. **Set Hourly Rates**: Configure default billing rates

### **Daily Operations**
1. **Create Bills**: Generate time-based bills for customers
2. **Process Payments**: Record payments with multiple methods
3. **Track Expenses**: Log business expenses with categories
4. **Generate Reports**: Create PDF reports for analysis
5. **Send Reminders**: Use WhatsApp/SMS for payment reminders

### **Advanced Features**
- **Multi-Account**: Switch between different business accounts
- **Backup Data**: Regular backups to secure your information
- **Custom Templates**: Personalize PDF and message templates
- **Analytics**: Use charts and reports for business insights

## 🔧 Configuration

### **App Settings**
- **Account Management**: Create and manage multiple business accounts
- **Currency Settings**: Set currency symbol and default hourly rates
- **PDF Templates**: Customize PDF layouts and company information
- **Message Templates**: Personalize WhatsApp and SMS templates
- **Backup Settings**: Configure automatic backup schedules

### **Customization Options**
- **Company Branding**: Add company information to PDFs
- **Color Themes**: Automatic dark/light theme support
- **Language Support**: Internationalization ready
- **Regional Settings**: Currency and date format customization

## 📊 Data Management

### **Data Models**
- **Bills**: Time-based billing with discounts and payment status
- **Customers**: Complete customer profiles with contact information
- **Payments**: Multiple payment methods with partial payment support
- **Expenses**: Categorized expense tracking
- **Accounts**: Multi-account system with data isolation

### **Backup & Security**
- **Local Backup**: SQLite database backup to device storage
- **Data Export**: PDF export for all major data types
- **Account Isolation**: Complete data separation between accounts
- **Secure Storage**: Encrypted local data storage

## 🎯 Performance Optimizations

### **APK Size Optimization**
- **R8 Full Mode**: Advanced code shrinking (~20-30% reduction)
- **Resource Shrinking**: Removes unused resources (~10-15% reduction)
- **ABI Filtering**: Targets specific architectures (~25-35% reduction)
- **ProGuard Rules**: Custom optimization rules (~5-10% reduction)
- **Expected Total Reduction**: 50-70% smaller APK size

### **Runtime Performance**
- **Provider State Management**: Efficient state handling
- **Lazy Loading**: On-demand data loading
- **Database Optimization**: Indexed queries and transactions
- **Memory Management**: Proper widget disposal and cleanup

## 🤝 Contributing

This is a proprietary application. For feature requests or bug reports, please contact the development team.

## 📄 License

© 2024 Tubewell Water Billing • All Rights Reserved

This software is proprietary and confidential. Unauthorized copying, distribution, or modification is strictly prohibited.

## 📞 Support

For technical support or feature requests, please contact:
- **Email**: <EMAIL>
- **Documentation**: Available in the app's Settings > App Information
- **Version**: 1.0.0 (Build 1)

---

**Built with ❤️ using Flutter for professional water billing management**
