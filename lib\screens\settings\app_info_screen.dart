import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';

class AppInfoScreen extends StatelessWidget {
  const AppInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'App Information',
        showPdfOption: false,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildAppHeader(),
          const SizedBox(height: 20),
          _buildFeaturesList(),
          const SizedBox(height: 20),
          _buildAppScreensSection(),
          const SizedBox(height: 20),
          _buildAboutSection(),
        ],
      ),
    );
  }

  Widget _buildAppHeader() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(
              Icons.water_drop,
              size: 64,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            const Text(
              'Tubewell Water Billing App',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.new_releases, size: 16, color: Colors.purple),
                  SizedBox(width: 8),
                  Text('Version: 1.0',
                      style: TextStyle(color: Colors.purple)),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Professional water billing management system with multi-account support, advanced reporting, PDF generation, and comprehensive financial tracking.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Key Features',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              icon: Icons.water_drop,
              title: 'Time-Based Water Billing',
              description:
                  'Accurate billing based on start/end times with automatic duration calculation and hourly rates.',
              color: Colors.blue,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.people,
              title: 'Customer Management',
              description:
                  'Complete customer database with contact information, billing history, and balance tracking.',
              color: Colors.green,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.payment,
              title: 'Payment Processing',
              description:
                  'Multiple payment methods (Cash, Bank Transfer, UPI, Check) with partial payment support.',
              color: Colors.indigo,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.account_balance_wallet,
              title: 'Multi-Account System',
              description:
                  'Manage multiple business accounts with complete data isolation and easy switching.',
              color: Colors.purple,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.credit_card,
              title: 'Customer Credit System',
              description:
                  'Track customer credits and automatically apply them to future bills with detailed history.',
              color: Colors.teal,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.discount,
              title: 'Discount Management',
              description:
                  'Apply time-based and amount-based discounts to bills with automatic calculations.',
              color: Colors.orange,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.receipt_long,
              title: 'Expense Tracking',
              description:
                  'Categorized expense management with detailed reporting and financial analysis.',
              color: Colors.red,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.picture_as_pdf,
              title: 'Professional PDF Reports',
              description:
                  'Generate customizable PDF bills, reports, and summaries with multiple templates.',
              color: Colors.deepPurple,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.message,
              title: 'WhatsApp & SMS Integration',
              description:
                  'Send bills and payment reminders via WhatsApp and SMS with custom templates.',
              color: Colors.green.shade700,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.analytics,
              title: 'Advanced Analytics',
              description:
                  'Comprehensive financial reports with charts, trends, and detailed breakdowns.',
              color: Colors.cyan,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.search,
              title: 'Smart Search & Filters',
              description:
                  'Advanced search and filtering across all data with multiple sorting options.',
              color: Colors.amber.shade700,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.backup,
              title: 'Data Backup & Restore',
              description:
                  'Secure local and cloud backup with scheduled automatic backups and easy restore.',
              color: Colors.brown,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.currency_exchange,
              title: 'Multi-Currency Support',
              description:
                  'Customizable currency settings with support for different regional currencies.',
              color: Colors.pink,
            ),
            const Divider(),
            _buildFeatureItem(
              icon: Icons.notifications,
              title: 'Payment Reminders',
              description:
                  'Automated reminder system for unpaid bills with customizable message templates.',
              color: Colors.deepOrange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(26),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppScreensSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'App Screens & Navigation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildScreenItem(
              icon: Icons.home,
              title: 'Dashboard & Summary',
              description: 'Real-time financial overview with charts and recent activity',
              color: Colors.blue.shade700,
            ),
            const Divider(),
            _buildScreenItem(
              icon: Icons.receipt_long,
              title: 'Bills & Transactions',
              description: 'Complete bill management with search, filter, and sorting',
              color: Colors.pink,
            ),
            const Divider(),
            _buildScreenItem(
              icon: Icons.people,
              title: 'Customer Management',
              description: 'Customer database with detailed profiles and history',
              color: Colors.purple,
            ),
            const Divider(),
            _buildScreenItem(
              icon: Icons.payment,
              title: 'Payment Tracking',
              description: 'Payment history with multiple methods and partial payments',
              color: Colors.green,
            ),
            const Divider(),
            _buildScreenItem(
              icon: Icons.receipt,
              title: 'Expense Management',
              description: 'Categorized expense tracking with detailed analytics',
              color: Colors.orange,
            ),
            const Divider(),
            _buildScreenItem(
              icon: Icons.settings,
              title: 'Settings & Configuration',
              description: 'Account management, currency settings, and app preferences',
              color: Colors.grey.shade700,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScreenItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(26),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'About',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'The Tubewell Water Billing App is a comprehensive, professional-grade solution designed specifically for tubewell operators and water billing businesses. It combines powerful billing capabilities with advanced financial management, customer relationship tools, and detailed reporting features.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            const Text(
              '🏗️ Technical Excellence',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Built with Flutter for cross-platform compatibility and Material Design 3 for modern UI. Uses SQLite for robust local data storage, ensuring your data is secure and accessible even without an internet connection. Features provider-based state management for optimal performance.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            const Text(
              '💼 Business Features',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Multi-account support allows managing multiple businesses or locations. Time-based billing with automatic duration calculations, discount management, customer credit systems, and comprehensive expense tracking. Professional PDF generation and WhatsApp/SMS integration for customer communication.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            const Text(
              '📊 Analytics & Reporting',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Advanced financial analytics with interactive charts, detailed expense categorization, payment method tracking, and comprehensive summary reports. Export capabilities for all major data types with customizable PDF templates.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.verified, size: 16, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Professional Grade Solution',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '© 2024 Tubewell Water Billing • All Rights Reserved',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
