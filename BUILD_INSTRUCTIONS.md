# Tubewell Water Billing - Build Instructions

## 🎯 Quick Start

### 1. Add Your App Icon
1. Save your blue water billing icon as `assets/icons/app_icon.png`
2. Recommended size: 1024x1024 pixels
3. Format: PNG with transparent or solid background

### 2. Build APK (Windows)
```bash
build_apk.bat
```

### 3. Build APK (Mac/Linux)
```bash
chmod +x build_apk.sh
./build_apk.sh
```

## 📱 APK Size Optimizations Applied

### 1. Code Optimization
- **R8 Full Mode**: Advanced code shrinking and obfuscation
- **ProGuard Rules**: Custom rules for Flutter and dependencies
- **Dead Code Elimination**: Removes unused code automatically

### 2. Resource Optimization
- **Resource Shrinking**: Removes unused resources
- **Image Compression**: Optimized icon generation
- **ABI Filtering**: Targets only ARM architectures (reduces ~30% size)

### 3. Build Configuration
- **Split Debug Info**: Debug symbols stored separately
- **Obfuscation**: Code obfuscation for smaller size
- **Target Platforms**: arm64-v8a, armeabi-v7a only

## 📊 Expected APK Size Reduction

| Optimization | Size Reduction |
|--------------|----------------|
| R8 Full Mode | ~20-30% |
| Resource Shrinking | ~10-15% |
| ABI Filtering | ~25-35% |
| ProGuard Rules | ~5-10% |
| **Total Expected** | **~50-70%** |

## 🚀 Build Outputs

### APK File (Direct Installation)
- **Location**: `build/app/outputs/flutter-apk/app-release.apk`
- **Use Case**: Direct installation on devices
- **Size**: Optimized for smallest possible size

### App Bundle (Play Store)
- **Location**: `build/app/outputs/bundle/release/app-release.aab`
- **Use Case**: Google Play Store upload
- **Advantage**: Dynamic delivery, even smaller downloads

## 🎨 Icon & Splash Screen Features

### App Icons Generated
- **Android**: All density variants (mdpi to xxxhdpi)
- **iOS**: All required sizes
- **Web**: Favicon and PWA icons
- **Desktop**: Windows, macOS, Linux icons

### Splash Screen
- **Background**: Blue theme matching your icon
- **Dark Mode**: Automatic dark theme support
- **Android 12+**: Modern splash screen API support
- **Branding**: Your icon prominently displayed

## 🔧 Manual Commands (if needed)

### Install Dependencies
```bash
flutter pub get
```

### Generate Icons
```bash
flutter pub run flutter_launcher_icons
```

### Generate Splash Screen
```bash
flutter pub run flutter_native_splash:create
```

### Build APK
```bash
flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info
```

### Build App Bundle
```bash
flutter build appbundle --release --shrink --obfuscate --split-debug-info=build/debug-info
```

## 📋 Troubleshooting

### Icon Not Showing
1. Ensure `app_icon.png` is in `assets/icons/` folder
2. Run `flutter pub run flutter_launcher_icons`
3. Clean and rebuild: `flutter clean && flutter pub get`

### Large APK Size
1. Verify ProGuard rules are applied
2. Check if R8 full mode is enabled
3. Ensure ABI filtering is working
4. Use App Bundle instead of APK for Play Store

### Build Errors
1. Update Flutter: `flutter upgrade`
2. Clean project: `flutter clean`
3. Get dependencies: `flutter pub get`
4. Check Android SDK is up to date

## 🎯 Performance Tips

1. **Use App Bundle** for Play Store (smaller downloads)
2. **Test on real devices** to verify optimizations
3. **Monitor APK size** with `flutter build apk --analyze-size`
4. **Profile performance** with `flutter run --profile`

## 📱 Installation

### Direct APK Installation
1. Enable "Unknown Sources" on Android device
2. Transfer APK file to device
3. Tap APK file to install

### Play Store Upload
1. Upload the `.aab` file to Play Console
2. Google Play will generate optimized APKs automatically
3. Users get the smallest possible download size

---

**Note**: The blue water billing icon will be automatically resized and optimized for all platforms. The splash screen will use the same icon with a matching blue background theme.
