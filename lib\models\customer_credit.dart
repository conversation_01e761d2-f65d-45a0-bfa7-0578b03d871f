class CustomerCredit {
  int id;
  int customerId;
  double amount;
  DateTime lastUpdated;
  String notes;

  // Named constructor with required parameters
  CustomerCredit.create({
    required this.customerId,
    required this.amount,
    this.notes = '',
  })  : id = 0,
        lastUpdated = DateTime.now();

  // Default constructor
  CustomerCredit({
    this.id = 0,
    required this.customerId,
    required this.amount,
    required this.lastUpdated,
    this.notes = '',
  });

  // Convert a CustomerCredit object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'customerId': customerId,
      'amount': amount,
      'lastUpdated': lastUpdated.toIso8601String(),
      'notes': notes,
    };
  }

  // Create a CustomerCredit object from a Map
  factory CustomerCredit.fromMap(Map<String, dynamic> map) {
    return CustomerCredit(
      id: map['id'],
      customerId: map['customerId'],
      amount: map['amount'],
      lastUpdated: DateTime.parse(map['lastUpdated']),
      notes: map['notes'] ?? '',
    );
  }

  // Clone method for creating a copy
  CustomerCredit clone() {
    return CustomerCredit(
      id: id,
      customerId: customerId,
      amount: amount,
      lastUpdated: lastUpdated,
      notes: notes,
    );
  }
}
