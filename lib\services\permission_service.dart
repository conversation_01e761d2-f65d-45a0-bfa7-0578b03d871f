import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  // Check and request storage permission
  static Future<bool> requestStoragePermission(BuildContext context) async {
    if (!Platform.isAndroid && !Platform.isIOS) {
      // For platforms other than Android and iOS, assume permission is granted
      return true;
    }

    // Check context state

    bool permissionGranted = false;

    if (Platform.isAndroid) {
      // For Android 11 (API 30) and above
      final isAndroid11Plus = await _isAndroid11OrAbove();

      if (!context.mounted) return false;

      if (isAndroid11Plus) {
        permissionGranted =
            await _requestManageExternalStoragePermission(context);
      } else {
        // For Android 10 and below
        permissionGranted = await _requestStoragePermission(context);
      }
    } else if (Platform.isIOS) {
      // iOS permission handling
      if (!context.mounted) return false;
      permissionGranted = await _requestStoragePermission(context);
    }

    return permissionGranted;
  }

  // Check if device is running Android 11 or above
  static Future<bool> _isAndroid11OrAbove() async {
    if (!Platform.isAndroid) return false;

    // Android 11 is API level 30
    return await Permission.manageExternalStorage.status.isGranted ||
        await Permission.manageExternalStorage.status.isDenied ||
        await Permission.manageExternalStorage.status.isPermanentlyDenied;
  }

  // Check storage permission status without requesting it
  static Future<bool> checkStoragePermissionStatus() async {
    if (!Platform.isAndroid && !Platform.isIOS) {
      // For platforms other than Android and iOS, assume permission is granted
      return true;
    }

    if (Platform.isAndroid) {
      // For Android 11 (API 30) and above
      final isAndroid11Plus = await _isAndroid11OrAbove();

      if (isAndroid11Plus) {
        final status = await Permission.manageExternalStorage.status;
        // Also check regular storage permission as a fallback
        final regularStatus = await Permission.storage.status;
        return status.isGranted || regularStatus.isGranted;
      } else {
        // For Android 10 and below
        final readStatus = await Permission.storage.status;
        final writeStatus = await Permission.accessMediaLocation.status;
        return readStatus.isGranted && writeStatus.isGranted;
      }
    } else if (Platform.isIOS) {
      // iOS permission handling
      final status = await Permission.storage.status;
      return status.isGranted;
    }

    return false;
  }

  // Request storage permission for Android 10 and below, and iOS
  static Future<bool> _requestStoragePermission(BuildContext context) async {
    // Check current status
    PermissionStatus storageStatus = await Permission.storage.status;
    PermissionStatus mediaStatus = await Permission.accessMediaLocation.status;

    // If all permissions are already granted, return true
    if (storageStatus.isGranted && mediaStatus.isGranted) {
      return true;
    }

    // Check if any permission is permanently denied
    if (storageStatus.isPermanentlyDenied || mediaStatus.isPermanentlyDenied) {
      // Show dialog explaining how to enable permission from settings
      if (!context.mounted) return false;
      final openSettings = await _showPermanentlyDeniedDialog(
        context,
        'Storage Permission',
        'Storage permission is required to access files. Please enable it in app settings.',
      );

      if (openSettings) {
        await openAppSettings();
      }

      return false;
    }

    // Request all necessary permissions
    List<Permission> permissions = [
      Permission.storage,
      Permission.accessMediaLocation,
    ];

    // Request permissions
    Map<Permission, PermissionStatus> statuses = await permissions.request();

    // Check if all permissions are granted
    bool allGranted = true;
    statuses.forEach((permission, status) {
      if (!status.isGranted) {
        allGranted = false;
      }
    });

    return allGranted;
  }

  // Request manage external storage permission for Android 11+
  static Future<bool> _requestManageExternalStoragePermission(
      BuildContext context) async {
    // Check current status
    PermissionStatus manageStatus = await Permission.manageExternalStorage.status;
    PermissionStatus storageStatus = await Permission.storage.status;

    // If manage external storage is already granted, return true
    if (manageStatus.isGranted) {
      return true;
    }

    // If regular storage permission is granted, we can try to use that as a fallback
    if (storageStatus.isGranted) {
      // For some operations, regular storage permission might be enough
      // Let's try to request manage external storage anyway
    }

    // Show explanation dialog before requesting permission
    if (!context.mounted) return false;
    final shouldRequest = await _showExplanationDialog(
      context,
      'Storage Permission Required',
      'This app needs full storage access to manage backup files. You\'ll need to grant "Allow management of all files" permission.',
      [
        'Tap "Allow access to manage all files"',
        'Toggle the switch to ON',
        'Press back to return to the app',
      ],
    );

    if (!shouldRequest) {
      return false;
    }

    // Request both permissions to maximize chances of success
    // First request regular storage permission
    await Permission.storage.request();

    // Then request manage external storage
    manageStatus = await Permission.manageExternalStorage.request();

    // Check if manage external storage is permanently denied
    if (manageStatus.isPermanentlyDenied) {
      // Show dialog explaining how to enable permission from settings
      if (!context.mounted) return false;
      final openSettings = await _showPermanentlyDeniedDialog(
        context,
        'Storage Permission',
        'Full storage access is required. Please enable "Allow management of all files" in app settings.',
      );

      if (openSettings) {
        await openAppSettings();
      }
    }

    // Check both permissions again
    manageStatus = await Permission.manageExternalStorage.status;
    storageStatus = await Permission.storage.status;

    // Return true if either permission is granted
    return manageStatus.isGranted || storageStatus.isGranted;
  }

  // Show explanation dialog before requesting permission
  static Future<bool> _showExplanationDialog(
    BuildContext context,
    String title,
    String message,
    List<String> steps,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  if (steps.isNotEmpty) ...[
                    const Text('Please follow these steps:'),
                    const SizedBox(height: 8),
                    ...steps.asMap().entries.map((entry) {
                      final index = entry.key;
                      final step = entry.value;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 24,
                              height: 24,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Colors.teal.shade700,
                                shape: BoxShape.circle,
                              ),
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(step)),
                          ],
                        ),
                      );
                    }),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Continue'),
              ),
            ],
          ),
        ) ??
        false;
  }

  // Show dialog for permanently denied permissions
  static Future<bool> _showPermanentlyDeniedDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Open Settings'),
              ),
            ],
          ),
        ) ??
        false;
  }

  // Request camera permission
  static Future<bool> requestCameraPermission(BuildContext context) async {
    // Check current status
    PermissionStatus status = await Permission.camera.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isPermanentlyDenied) {
      // Show dialog explaining how to enable permission from settings
      if (!context.mounted) return false;
      final openSettings = await _showPermanentlyDeniedDialog(
        context,
        'Camera Permission',
        'Camera permission is required to take photos. Please enable it in app settings.',
      );

      if (openSettings) {
        await openAppSettings();
      }

      return false;
    }

    // Show explanation dialog before requesting permission
    if (!context.mounted) return false;
    final shouldRequest = await _showExplanationDialog(
      context,
      'Camera Permission Required',
      'This app needs camera access to take photos.',
      [],
    );

    if (!shouldRequest) {
      return false;
    }

    // Request permission
    status = await Permission.camera.request();
    return status.isGranted;
  }



  // Request notification permission
  static Future<bool> requestNotificationPermission(BuildContext context) async {
    // Check current status
    PermissionStatus status = await Permission.notification.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isPermanentlyDenied) {
      // Show dialog explaining how to enable permission from settings
      if (!context.mounted) return false;
      final openSettings = await _showPermanentlyDeniedDialog(
        context,
        'Notification Permission',
        'Notification permission is required to receive important alerts. Please enable it in app settings.',
      );

      if (openSettings) {
        await openAppSettings();
      }

      return false;
    }

    // Show explanation dialog before requesting permission
    if (!context.mounted) return false;
    final shouldRequest = await _showExplanationDialog(
      context,
      'Notification Permission Required',
      'This app needs to send notifications for important alerts and reminders.',
      [],
    );

    if (!shouldRequest) {
      return false;
    }

    // Request permission
    status = await Permission.notification.request();
    return status.isGranted;
  }

  // Check if all essential permissions are granted
  static Future<Map<String, bool>> checkEssentialPermissions() async {
    Map<String, bool> permissionStatus = {
      'storage': false,
      'camera': false,
      'notification': false,
      'internet': true, // Internet permission is not runtime permission
    };

    // Check storage permission
    permissionStatus['storage'] = await checkStoragePermissionStatus();

    // Check camera permission
    permissionStatus['camera'] = await Permission.camera.status.isGranted;

    // Check notification permission
    permissionStatus['notification'] = await Permission.notification.status.isGranted;

    return permissionStatus;
  }

  // Request all essential permissions at once
  static Future<bool> requestAllEssentialPermissions(BuildContext context) async {
    if (!context.mounted) return false;

    // Request storage permission
    final storageGranted = await requestStoragePermission(context);

    if (!context.mounted) return false;

    // Request camera permission
    final cameraGranted = await requestCameraPermission(context);

    if (!context.mounted) return false;

    // Request notification permission
    final notificationGranted = await requestNotificationPermission(context);

    // Return true only if all permissions are granted
    return storageGranted && cameraGranted && notificationGranted;
  }

  // Request multiple permissions at once
  static Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(
    BuildContext context,
    List<Permission> permissions,
    String title,
    String message,
  ) async {
    // Check if any permission is permanently denied
    bool anyPermanentlyDenied = false;
    for (var permission in permissions) {
      if (await permission.status.isPermanentlyDenied) {
        anyPermanentlyDenied = true;
        break;
      }
    }

    if (anyPermanentlyDenied) {
      // Show dialog explaining how to enable permissions from settings
      if (!context.mounted) {
        // Return current statuses if context is no longer mounted
        Map<Permission, PermissionStatus> statuses = {};
        for (var permission in permissions) {
          statuses[permission] = await permission.status;
        }
        return statuses;
      }

      final openSettings = await _showPermanentlyDeniedDialog(
        context,
        title,
        '$message Please enable them in app settings.',
      );

      if (openSettings) {
        await openAppSettings();
      }

      // Return current statuses
      Map<Permission, PermissionStatus> statuses = {};
      for (var permission in permissions) {
        statuses[permission] = await permission.status;
      }
      return statuses;
    }

    // Show explanation dialog before requesting permissions
    if (!context.mounted) {
      // Return current statuses if context is no longer mounted
      Map<Permission, PermissionStatus> statuses = {};
      for (var permission in permissions) {
        statuses[permission] = await permission.status;
      }
      return statuses;
    }

    final shouldRequest = await _showExplanationDialog(
      context,
      title,
      message,
      [],
    );

    if (!shouldRequest) {
      // Return current statuses
      Map<Permission, PermissionStatus> statuses = {};
      for (var permission in permissions) {
        statuses[permission] = await permission.status;
      }
      return statuses;
    }

    // Request permissions
    return await permissions.request();
  }
}
