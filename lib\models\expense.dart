class Expense {
  int id;
  String description;
  double amount;
  DateTime date;
  String category;
  String? paymentMethod;
  String? remarks;

  // Named constructor with required parameters
  Expense.create({
    required this.description,
    required this.amount,
    required this.date,
    required this.category,
    this.paymentMethod,
    this.remarks,
  }) : id = 0;

  // Default constructor
  Expense({
    this.id = 0,
    required this.description,
    required this.amount,
    required this.date,
    required this.category,
    this.paymentMethod,
    this.remarks,
  });

  // Convert an Expense object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'description': description,
      'amount': amount,
      'date': date.toIso8601String(),
      'category': category,
      'paymentMethod': paymentMethod,
      'remarks': remarks,
    };
  }

  // Create an Expense object from a Map
  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      id: map['id'],
      description: map['description'],
      amount: map['amount'],
      date: DateTime.parse(map['date']),
      category: map['category'],
      paymentMethod: map['paymentMethod'],
      remarks: map['remarks'],
    );
  }

  // Clone method for creating a copy
  Expense clone() {
    return Expense(
      id: id,
      description: description,
      amount: amount,
      date: date,
      category: category,
      paymentMethod: paymentMethod,
      remarks: remarks,
    );
  }
}
