import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/background_backup_service.dart';
import 'package:tubewell_water_billing/services/permission_service.dart';
import 'package:tubewell_water_billing/widgets/bottom_navigation.dart';
import 'package:tubewell_water_billing/screens/reminder_screen.dart';
import 'package:tubewell_water_billing/screens/settings/pdf_settings_screen.dart';
import 'package:tubewell_water_billing/screens/settings/settings_screen.dart';
import 'package:tubewell_water_billing/providers/pdf_settings_provider.dart';
import 'package:tubewell_water_billing/utils/page_transitions.dart';
import 'package:tubewell_water_billing/config/app_config.dart';
import 'package:logger/logger.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Configure logger for production
  Logger.level = AppConfig.isProduction ? Level.error : Level.trace;

  // Initialize shared preferences for account service
  await SharedPreferences.getInstance();

  // Initialize the account service first
  await AccountService.initialize();

  // Initialize the database with the current account
  final currentAccount = AccountService.currentAccount;
  await DatabaseService.initialize(accountId: currentAccount?.id);

  // Initialize the currency service
  await CurrencyService.initialize();

  // Initialize the background backup service (placeholder while WorkManager disabled)
  await BackgroundBackupService.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<DatabaseService>(create: (_) => DatabaseService()),
        ChangeNotifierProvider<PdfSettingsProvider>(create: (_) => PdfSettingsProvider()),
      ],
      child: MaterialApp(
        title: 'Tubewell Water Billing',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          primaryColor: const Color(0xFF2E7D32),
          colorScheme: ColorScheme.fromSwatch().copyWith(
            primary: const Color(0xFF2E7D32),
            secondary: const Color(0xFF66BB6A),
          ),
          useMaterial3: true,
        ),
        darkTheme: ThemeData.dark(useMaterial3: true),
        themeMode: ThemeMode.system,
        home: const StartScreen(),
        debugShowCheckedModeBanner: false,
        routes: {
          '/reminders': (context) => const ReminderScreen(),
          '/pdf_settings': (context) => const PdfSettingsScreen(),
          '/settings': (context) => const SettingsScreenImpl(),
        },
      ),
    );
  }
}

class StartScreen extends StatefulWidget {
  const StartScreen({super.key});

  @override
  State<StartScreen> createState() => _StartScreenState();
}

class _StartScreenState extends State<StartScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // Ensure account service is initialized
      await AccountService.initialize();

      // Initialize the database with the current account
      final currentAccount = AccountService.currentAccount;
      await DatabaseService.initialize(accountId: currentAccount?.id);

      // Initialize the currency service
      await CurrencyService.initialize();

      // Initialize the background backup service (placeholder while WorkManager disabled)
      await BackgroundBackupService.initialize();

      // Add a small delay to ensure smooth transition
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      // Error handled silently
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _requestPermissions() async {
    if (!mounted) return;

    try {
      // Request essential permissions after the app has loaded
      await PermissionService.requestAllEssentialPermissions(context);
    } catch (e) {
      // Handle permission errors silently
      // The permission service will show appropriate dialogs to the user
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/logo.png',
                width: 120,
                height: 120,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(Icons.water_drop,
                      size: 80, color: Color(0xFF2E7D32));
                },
              ),
              const SizedBox(height: 20),
              const Text(
                'Tubewell Water Billing',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
              const SizedBox(height: 20),
              const CircularProgressIndicator(
                color: Color(0xFF2E7D32),
              ),
            ],
          ),
        ),
      );
    }

    // Navigate to the main screen once initialization is complete with smooth transition
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Navigator.of(context).pushReplacementFade(
        const BottomNavigation(),
        duration: const Duration(milliseconds: 400),
      );

      // Request permissions after navigation is complete
      Future.delayed(const Duration(milliseconds: 500), () {
        _requestPermissions();
      });
    });

    // Return a placeholder until navigation completes
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(
          color: Color(0xFF2E7D32),
        ),
      ),
    );
  }
}
