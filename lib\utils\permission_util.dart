import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/services/permission_service.dart';
import 'package:tubewell_water_billing/widgets/permission_error_dialog.dart';

/// Utility class for handling permissions in the app
class PermissionUtil {
  /// Check if all essential permissions are granted
  /// Returns a map of permission names to boolean values
  static Future<Map<String, bool>> checkAllPermissions() async {
    return await PermissionService.checkEssentialPermissions();
  }

  /// Show a dialog to request all essential permissions
  /// This is useful to call at app startup
  static Future<bool> requestEssentialPermissions(BuildContext context) async {
    // First check if we already have all permissions
    final permissionStatus = await checkAllPermissions();
    
    // Check if any permission is missing
    bool allGranted = true;
    permissionStatus.forEach((permission, granted) {
      if (!granted) {
        allGranted = false;
      }
    });
    
    // If all permissions are already granted, return true
    if (allGranted) {
      return true;
    }
    
    // Show a dialog to request all permissions
    bool permissionsGranted = false;
    
    if (context.mounted) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => PermissionErrorDialog(
          title: 'Permissions Required',
          message: 'This app requires several permissions to function properly. '
              'Please grant all the requested permissions to ensure the app works correctly.',
          permissionType: 'all',
          onPermissionGranted: () {
            permissionsGranted = true;
          },
          onCancel: () {
            // Do nothing, dialog will be dismissed
          },
        ),
      );
    }
    
    // Check permissions again after dialog is closed
    final updatedStatus = await checkAllPermissions();
    
    // Check if all permissions are now granted
    bool nowAllGranted = true;
    updatedStatus.forEach((permission, granted) {
      if (!granted) {
        nowAllGranted = false;
      }
    });
    
    return nowAllGranted || permissionsGranted;
  }
  
  /// Show a dialog for a specific permission
  static Future<bool> requestSpecificPermission(
    BuildContext context, 
    String permissionType,
    String message,
  ) async {
    bool permissionGranted = false;
    
    if (context.mounted) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => PermissionErrorDialog(
          title: '${permissionType.substring(0, 1).toUpperCase()}${permissionType.substring(1)} Permission Required',
          message: message,
          permissionType: permissionType,
          onPermissionGranted: () {
            permissionGranted = true;
          },
          onCancel: () {
            // Do nothing, dialog will be dismissed
          },
        ),
      );
    }
    
    return permissionGranted;
  }
}
