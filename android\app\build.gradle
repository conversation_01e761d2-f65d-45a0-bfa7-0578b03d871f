plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.example.tubewell_water_billing"
    compileSdk = 35
    ndkVersion = "25.1.8937393"

    // Additional size optimizations
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    defaultConfig {
        // Professional water billing management application
        applicationId = "com.tubewellbilling.app"
        // Optimized for modern Android devices
        minSdk = 23
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"


    }

    splits {
        abi {
            enable true
            reset()
            include 'arm64-v8a', 'armeabi-v7a', 'x86_64'
            universalApk false
        }
        density {
            enable true
            reset()
            include 'mdpi', 'hdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug

            // Enable aggressive optimizations for smaller APK size
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Additional size optimizations
            zipAlignEnabled true
            crunchPngs true
        }
    }
}

flutter {
    source = "../.."
}
