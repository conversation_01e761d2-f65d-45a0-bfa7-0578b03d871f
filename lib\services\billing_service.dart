import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/payment_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';

/// A service for centralizing bill operations with proper payment handling
///
/// This service combines all bill-related operations including:
/// - Bill payment status management
/// - Bill deletion with proper payment handling
/// - Bill filtering and querying
/// - Credit application to bills
class BillingService {
  /// Toggle the payment status of a bill
  /// This method ensures proper data consistency by:
  /// 1. If marking as paid, recording a payment if none exists
  /// 2. If marking as unpaid, removing any associated payments
  static Future<bool> toggleBillPaymentStatus(Bill bill) async {
    try {
      // Check current state to determine the action
      final isPaid = bill.isPaid;
      final updatedBill = bill.clone();

      if (isPaid) {
        // Currently paid, marking as unpaid
        // First, check if there are payments associated with this bill
        final payments = await DatabaseService.getPaymentsByBill(bill.id);

        // If payments exist, we need to handle them properly
        if (payments.isNotEmpty) {
          // For each payment associated with this bill, delete it
          for (final payment in payments) {
            await PaymentService.deletePaymentAndUpdateBillStatus(payment);
          }

          // The bill status should have been updated by the payment service,
          // so we don't need to update it again
          return true;
        } else {
          // No payments, just toggle the status
          updatedBill.isPaid = false;
          updatedBill.paidDate = null;
          await DatabaseService.updateBill(updatedBill);
          return true;
        }
      } else {
        // Currently unpaid, marking as paid
        // Check if there are already partial payments
        final payments = await DatabaseService.getPaymentsByBill(bill.id);
        final totalPaid = payments.fold<double>(0, (sum, p) => sum + p.amount);

        if (payments.isEmpty || totalPaid < bill.amount) {
          // Get current customer credit
          final creditAmount =
              await PaymentService.getCustomerCreditBalance(bill.customerId);

          // Calculate how much actual payment is needed after using credit
          double remainingAmount = bill.amount - totalPaid;
          double paymentAmount = remainingAmount;

          // If there's available credit, use it for this bill
          if (creditAmount > 0) {
            // If credit covers the full amount, we don't need a new payment
            if (creditAmount >= remainingAmount) {
              paymentAmount = 0;

              // Process a zero payment to apply the credit correctly
              await PaymentService.processPayment(
                customerId: bill.customerId,
                amount: 0, // Zero payment amount
                paymentDate: DateTime.now(),
                paymentMethod: 'Credit',
                remarks: 'Marked as paid using available credit',
                targetBillId: bill.id,
              );

              return true;
            } else {
              // Partial credit - reduce payment amount by available credit
              paymentAmount = remainingAmount - creditAmount;
            }
          }

          // Create a payment for any remaining amount (after using credit)
          if (paymentAmount > 0) {
            await PaymentService.processPayment(
              customerId: bill.customerId,
              amount: paymentAmount,
              paymentDate: DateTime.now(),
              paymentMethod: 'Auto-marked as paid',
              remarks:
                  'Marked as paid manually${creditAmount > 0 ? ' (partially using credit)' : ''}',
              targetBillId: bill.id,
            );
          }

          // The bill status should be updated by the payment service
          return true;
        } else {
          // Already has full payment but status is wrong, just update status
          updatedBill.isPaid = true;
          updatedBill.isPartiallyPaid = false;
          updatedBill.paidDate = DateTime.now();
          await DatabaseService.updateBill(updatedBill);
          return true;
        }
      }
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }

  /// Delete a bill with proper handling of linked payments
  /// This method ensures data consistency by:
  /// 1. Removing any payments linked to this bill
  /// 2. Updating customer credit and balances
  static Future<bool> deleteBill(Bill bill) async {
    try {
      // First, get any payments associated with this bill
      final payments = await DatabaseService.getPaymentsByBill(bill.id);

      // Delete each payment with proper handling
      for (final payment in payments) {
        await PaymentService.deletePaymentAndUpdateBillStatus(payment);
      }

      // Now delete the bill itself
      final result = await DatabaseService.deleteBill(bill.id);
      return result;
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }

  /// Update a bill's payment status based on linked payments
  static Future<void> updateBillPaymentStatus(int billId) async {
    try {
      // Get the bill
      final bill = await DatabaseService.getBillById(billId);
      if (bill == null) return;

      // Get payments for this bill
      final payments = await DatabaseService.getPaymentsByBill(billId);
      final totalPaid = payments.fold<double>(0, (sum, p) => sum + p.amount);

      // Update bill status based on payments
      final updatedBill = bill.clone();

      if (totalPaid >= bill.amount) {
        // Fully paid
        updatedBill.isPaid = true;
        updatedBill.isPartiallyPaid = false;
        updatedBill.partialAmount = null;

        // Set paid date to the latest payment date if available
        if (payments.isNotEmpty) {
          payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
          updatedBill.paidDate = payments.first.paymentDate;
        } else {
          updatedBill.paidDate = DateTime.now();
        }
      } else if (totalPaid > 0) {
        // Partially paid
        updatedBill.isPaid = false;
        updatedBill.isPartiallyPaid = true;
        updatedBill.partialAmount = totalPaid;
      } else {
        // No payments
        updatedBill.isPaid = false;
        updatedBill.isPartiallyPaid = false;
        updatedBill.partialAmount = null;
        updatedBill.paidDate = null;
      }

      await DatabaseService.updateBill(updatedBill);
    } catch (e) {
      // Error handled silently
    }
  }

  /// Get the outstanding amount for a bill
  static double getOutstandingAmount(Bill bill) {
    if (bill.isPaid) return 0;

    if (bill.isPartiallyPaid && bill.partialAmount != null) {
      return bill.amount - bill.partialAmount!;
    }

    return bill.amount;
  }

  /// Check for available credit and apply it to a bill if needed
  /// This method will:
  /// 1. Check if the bill is already paid - if so, do nothing
  /// 2. Check if the customer has available credit
  /// 3. Apply the credit to the bill and update credit balance
  /// 4. Return true if credit was applied, false otherwise
  static Future<bool> checkAndApplyCreditToBill(Bill bill) async {
    try {
      // Skip if bill is already paid
      if (bill.isPaid) return false;

      // Get current customer credit
      final creditAmount =
          await PaymentService.getCustomerCreditBalance(bill.customerId);

      // If no credit available, nothing to do
      if (creditAmount <= 0) return false;

      // Calculate how much is needed to pay the bill
      double remainingAmount = bill.amount;
      if (bill.isPartiallyPaid && bill.partialAmount != null) {
        remainingAmount -= bill.partialAmount!;
      }

      // If no remaining amount, nothing to do
      if (remainingAmount <= 0) return false;

      // Determine how much credit to apply
      double creditToApply =
          creditAmount >= remainingAmount ? remainingAmount : creditAmount;

      if (creditToApply > 0) {
        // Update the bill status
        final updatedBill = bill.clone();
        updatedBill.isPaid = true;
        updatedBill.isPartiallyPaid = false;
        updatedBill.paidDate = DateTime.now();

        // Save the updated bill
        await DatabaseService.updateBill(updatedBill);

        // Calculate the new credit balance
        double newCreditBalance = creditAmount - creditToApply;

        // Update the customer credit balance
        await DatabaseService.updateCustomerCreditAmount(bill.customerId,
            newCreditBalance, 'Credit used to pay bill #${bill.id}');

        // Create a payment record to track this transaction
        final payment = Payment.create(
          customerId: bill.customerId,
          billId: bill.id,
          paymentDate: DateTime.now(),
          amount: 0, // Zero amount indicates using only credit
          paymentMethod: 'Credit',
          remarks:
              'Automatically paid using available credit (${CurrencyService.formatCurrency(creditToApply)})',
        );

        // Save the payment
        await DatabaseService.savePayment(payment);

        return true;
      }

      return false;
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }

  /// Safely delete a bill and handle any linked payments
  ///
  /// This method will:
  /// 1. Find any payments linked to this bill
  /// 2. Convert those payments to customer credit
  /// 3. Delete the bill
  static Future<bool> deleteBillSafely(int billId) async {
    try {
      // First fetch the bill to get the customerId
      final bill = await DatabaseService.getBillById(billId);
      if (bill == null) {
        // Bill not found
        return false;
      }

      final int customerId = bill.customerId;

      // Get any payments linked to this bill
      final payments = await DatabaseService.getPaymentsByBill(billId);

      if (payments.isNotEmpty) {
        // Process payments linked to this bill

        // For each payment, unlink it from the bill and convert to credit
        for (final payment in payments) {
          final updatedPayment = payment.clone();

          // Update payment to unlink from bill
          updatedPayment.billId =
              0; // Change to 0 (credit) instead of null for consistency

          // Add a note to the remarks about the bill deletion
          final existingRemarks = updatedPayment.remarks ?? '';
          updatedPayment.remarks =
              '$existingRemarks${existingRemarks.isNotEmpty ? ' ' : ''}(Converted to credit - Bill #$billId was deleted)';

          // Save the updated payment
          await DatabaseService.savePayment(updatedPayment);
        }

        // Update customer credit
        // Recalculate customer credit by processing a zero payment
        await PaymentService.processPayment(
          customerId: customerId,
          amount: 0,
          paymentDate: DateTime.now(),
          remarks: 'Credit recalculation after bill deletion',
        );
      }

      // Finally delete the bill
      final deleted = await DatabaseService.deleteBill(billId);

      return deleted;
    } catch (e) {
      // Error handled by returning false
      return false;
    }
  }

  /// Get filtered and paginated list of bills
  static Future<List<Bill>> getBillsFiltered({
    int? customerId,
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    String? searchQuery,
    int offset = 0,
    int limit = 20,
    String sortBy = 'date',
    bool sortDescending = true,
  }) async {
    // Use DatabaseService if it has the filtering function already
    try {
      return await DatabaseService.getBillsFiltered(
        customerId: customerId,
        startDate: startDate,
        endDate: endDate,
        isPaid: isPaid,
        searchQuery: searchQuery,
        offset: offset,
        limit: limit,
        sortDescending: sortDescending,
      );
    } catch (e) {
      // Fallback to basic retrieval if the method doesn't exist
      List<Bill> bills;

      if (customerId != null) {
        bills = await DatabaseService.getBillsByCustomer(customerId);
      } else {
        bills = await DatabaseService.getAllBills();
      }

      // Apply basic sorting
      bills.sort((a, b) => sortDescending
          ? b.billDate.compareTo(a.billDate)
          : a.billDate.compareTo(b.billDate));

      // Return a slice for basic pagination
      final end = offset + limit < bills.length ? offset + limit : bills.length;
      if (offset >= bills.length) return [];
      return bills.sublist(offset, end);
    }
  }

  /// Count total bills matching filter criteria
  static Future<int> countBills({
    int? customerId,
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    String? searchQuery,
  }) async {
    try {
      return await DatabaseService.countBillsFiltered(
        customerId: customerId,
        startDate: startDate,
        endDate: endDate,
        isPaid: isPaid,
        searchQuery: searchQuery,
      );
    } catch (e) {
      // Fallback to basic count on error

      // Fallback to basic count
      if (customerId != null) {
        final bills = await DatabaseService.getBillsByCustomer(customerId);
        return bills.length;
      } else {
        final bills = await DatabaseService.getAllBills();
        return bills.length;
      }
    }
  }

  /// Get a summary of bills matching filter criteria
  static Future<Map<String, num>> getBillsSummary({
    int? customerId,
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    String? searchQuery,
  }) async {
    try {
      return await DatabaseService.getBillsSummary(
        customerId: customerId,
        startDate: startDate,
        endDate: endDate,
        isPaid: isPaid,
        searchQuery: searchQuery,
      );
    } catch (e) {
      // Return empty summary on error
      return {
        'totalCount': 0,
        'totalAmount': 0,
        'paidAmount': 0,
        'unpaidAmount': 0,
        'paidCount': 0,
        'unpaidCount': 0
      };
    }
  }
}
