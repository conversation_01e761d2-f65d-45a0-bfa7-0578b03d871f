import 'package:logger/logger.dart';

/// Configuration settings for the application
class AppConfig {
  /// Whether the app is in production mode
  static const bool isProduction = true;

  /// Get a logger instance configured for the current environment
  static Logger getLogger() {
    if (isProduction) {
      // In production, only log errors and nothing else
      return Logger(
        filter: ProductionFilter(),
        printer: SimplePrinter(),
        output: null, // Disable output completely
      );
    } else {
      // In development, log everything
      return Logger(
        filter: DevelopmentFilter(),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        ),
      );
    }
  }
}

/// Filter that only allows error logs in production
class ProductionFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // Only log errors and fatal issues in production
    return event.level.index >= Level.error.index;
  }
}
