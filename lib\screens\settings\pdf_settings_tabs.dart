import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/providers/pdf_settings_provider.dart';

import 'package:flutter_colorpicker/flutter_colorpicker.dart';

class ColorsTab extends StatefulWidget {
  const ColorsTab({super.key});

  @override
  State<ColorsTab> createState() => _ColorsTabState();
}

class _ColorsTabState extends State<ColorsTab> {
  Color? _tempPrimaryColor;
  Color? _tempAccentColor;
  Color? _tempTextColor;

  @override
  Widget build(BuildContext context) {
    final pdfSettings = Provider.of<PdfSettingsProvider>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Color Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Primary color
          _buildColorSetting(
            context,
            title: 'Primary Color',
            color: pdfSettings.primaryColor,
            onTap: () => _showColorPicker(
              context,
              'Primary Color',
              pdfSettings.primaryColor,
              (color) {
                setState(() => _tempPrimaryColor = color);
              },
              () {
                if (_tempPrimaryColor != null) {
                  pdfSettings.setPrimaryColor(_tempPrimaryColor!);
                  _tempPrimaryColor = null;
                }
              },
            ),
          ),

          // Accent color
          _buildColorSetting(
            context,
            title: 'Accent Color',
            color: pdfSettings.accentColor,
            onTap: () => _showColorPicker(
              context,
              'Accent Color',
              pdfSettings.accentColor,
              (color) {
                setState(() => _tempAccentColor = color);
              },
              () {
                if (_tempAccentColor != null) {
                  pdfSettings.setAccentColor(_tempAccentColor!);
                  _tempAccentColor = null;
                }
              },
            ),
          ),

          // Text color
          _buildColorSetting(
            context,
            title: 'Text Color',
            color: pdfSettings.textColor,
            onTap: () => _showColorPicker(
              context,
              'Text Color',
              pdfSettings.textColor,
              (color) {
                setState(() => _tempTextColor = color);
              },
              () {
                if (_tempTextColor != null) {
                  pdfSettings.setTextColor(_tempTextColor!);
                  _tempTextColor = null;
                }
              },
            ),
          ),

          const Divider(height: 32),

          // Color presets
          const Text(
            'Color Presets',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildColorPreset(
                context,
                title: 'Modern Blue/Green',
                primaryColor: const Color(0xFF2563EB),
                accentColor: const Color(0xFF10B981),
                onTap: () {
                  pdfSettings.setPrimaryColor(const Color(0xFF2563EB));
                  pdfSettings.setAccentColor(const Color(0xFF10B981));
                },
              ),
              _buildColorPreset(
                context,
                title: 'Professional Blue/Green',
                primaryColor: const Color(0xFF1976D2),
                accentColor: const Color(0xFF2E7D32),
                onTap: () {
                  pdfSettings.setPrimaryColor(const Color(0xFF1976D2));
                  pdfSettings.setAccentColor(const Color(0xFF2E7D32));
                },
              ),
              _buildColorPreset(
                context,
                title: 'Purple/Teal',
                primaryColor: const Color(0xFF6200EA),
                accentColor: const Color(0xFF00BFA5),
                onTap: () {
                  pdfSettings.setPrimaryColor(const Color(0xFF6200EA));
                  pdfSettings.setAccentColor(const Color(0xFF00BFA5));
                },
              ),
              _buildColorPreset(
                context,
                title: 'Dark/Neutral',
                primaryColor: const Color(0xFF263238),
                accentColor: const Color(0xFF78909C),
                onTap: () {
                  pdfSettings.setPrimaryColor(const Color(0xFF263238));
                  pdfSettings.setAccentColor(const Color(0xFF78909C));
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorSetting(
    BuildContext context, {
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(0, 0, 0, 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '#${(color.r.toInt() << 16 | color.g.toInt() << 8 | color.b.toInt()).toRadixString(16).padLeft(6, '0').toUpperCase()}',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontFamily: 'monospace',
              ),
            ),
            const Spacer(),
            const Icon(Icons.edit, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildColorPreset(
    BuildContext context, {
    required String title,
    required Color primaryColor,
    required Color accentColor,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: MediaQuery.of(context).size.width / 2 - 24,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 30,
                    decoration: BoxDecoration(
                      color: primaryColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 30,
                    decoration: BoxDecoration(
                      color: accentColor,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showColorPicker(
    BuildContext context,
    String title,
    Color initialColor,
    ValueChanged<Color> onColorChanged,
    VoidCallback onSave,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select $title'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: initialColor,
            onColorChanged: onColorChanged,
            pickerAreaHeightPercent: 0.8,
            enableAlpha: false,
            displayThumbColor: true,
            paletteType: PaletteType.hsv,
            pickerAreaBorderRadius: const BorderRadius.all(Radius.circular(10)),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              onSave();
              Navigator.pop(context);
            },
            child: const Text('SAVE'),
          ),
        ],
      ),
    );
  }
}

class CompanyInfoTab extends StatelessWidget {
  const CompanyInfoTab({super.key});

  @override
  Widget build(BuildContext context) {
    final pdfSettings = Provider.of<PdfSettingsProvider>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Company Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Company name
          _buildTextField(
            label: 'Company Name',
            initialValue: pdfSettings.companyName,
            onChanged: (value) => pdfSettings.setCompanyName(value),
          ),

          // Company address
          _buildTextField(
            label: 'Company Address (Optional)',
            initialValue: pdfSettings.companyAddress ?? '',
            onChanged: (value) => pdfSettings.setCompanyAddress(value.isEmpty ? null : value),
          ),

          // Company phone
          _buildTextField(
            label: 'Phone Number (Optional)',
            initialValue: pdfSettings.companyPhone ?? '',
            onChanged: (value) => pdfSettings.setCompanyPhone(value.isEmpty ? null : value),
          ),

          // Company email
          _buildTextField(
            label: 'Email Address (Optional)',
            initialValue: pdfSettings.companyEmail ?? '',
            onChanged: (value) => pdfSettings.setCompanyEmail(value.isEmpty ? null : value),
          ),

          // Company website
          _buildTextField(
            label: 'Website (Optional)',
            initialValue: pdfSettings.companyWebsite ?? '',
            onChanged: (value) => pdfSettings.setCompanyWebsite(value.isEmpty ? null : value),
          ),

          const SizedBox(height: 24),

          // Preview
          const Text(
            'Preview',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  pdfSettings.companyName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                if (pdfSettings.companyAddress != null) ...[
                  Text(pdfSettings.companyAddress!),
                  const SizedBox(height: 4),
                ],
                if (pdfSettings.companyPhone != null) ...[
                  Text('Phone: ${pdfSettings.companyPhone}'),
                  const SizedBox(height: 4),
                ],
                if (pdfSettings.companyEmail != null) ...[
                  Text('Email: ${pdfSettings.companyEmail}'),
                  const SizedBox(height: 4),
                ],
                if (pdfSettings.companyWebsite != null) ...[
                  Text('Web: ${pdfSettings.companyWebsite}'),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required String initialValue,
    required ValueChanged<String> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextField(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        controller: TextEditingController(text: initialValue),
        onChanged: onChanged,
      ),
    );
  }
}
