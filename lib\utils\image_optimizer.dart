import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:logger/logger.dart';

/// A utility class for optimizing images in the app
class ImageOptimizer {
  static final Logger _logger = Logger();
  /// Compress an image file and return the compressed file
  static Future<File?> compressImage(File file, {int quality = 85}) async {
    try {
      // Get the file extension
      final extension = path.extension(file.path).toLowerCase();

      // Get the temporary directory
      final tempDir = await getTemporaryDirectory();
      final targetPath = path.join(tempDir.path,
          '${path.basenameWithoutExtension(file.path)}_compressed$extension');

      // Compress the image based on its format
      File? result;
      if (extension == '.png') {
        result = await _compressPng(file, targetPath, quality);
      } else if (extension == '.jpg' || extension == '.jpeg') {
        result = await _compressJpg(file, targetPath, quality);
      } else {
        // For other formats, just return the original file
        return file;
      }

      return result;
    } catch (e) {
      _logger.e('Error compressing image: $e');
      return null;
    }
  }

  /// Compress a PNG image
  static Future<File?> _compressPng(
      File file, String targetPath, int quality) async {
    final result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: quality,
      format: CompressFormat.png,
    );

    return result != null ? File(result.path) : null;
  }

  /// Compress a JPG image
  static Future<File?> _compressJpg(
      File file, String targetPath, int quality) async {
    final result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: quality,
      format: CompressFormat.jpeg,
    );

    return result != null ? File(result.path) : null;
  }

  /// Optimize all images in a directory
  static Future<void> optimizeImagesInDirectory(String dirPath) async {
    try {
      final dir = Directory(dirPath);
      if (!await dir.exists()) {
        _logger.w('Directory does not exist: $dirPath');
        return;
      }

      // Get all files in the directory
      final List<FileSystemEntity> entities = await dir.list().toList();

      // Filter image files
      final List<File> imageFiles = entities
          .whereType<File>()
          .where((file) {
            final ext = path.extension(file.path).toLowerCase();
            return ext == '.png' || ext == '.jpg' || ext == '.jpeg';
          })
          .toList();

      // Compress each image
      for (final file in imageFiles) {
        final originalSize = await file.length();
        final compressedFile = await compressImage(file);

        if (compressedFile != null) {
          final compressedSize = await compressedFile.length();

          // Only replace if the compressed file is smaller
          if (compressedSize < originalSize) {
            // Replace the original file with the compressed one
            await compressedFile.copy(file.path);
            await compressedFile.delete();

            _logger.i('Optimized ${file.path}: ${_formatFileSize(originalSize)} -> ${_formatFileSize(compressedSize)}');
          } else {
            await compressedFile.delete();
            _logger.i('Skipped ${file.path}: Already optimized');
          }
        }
      }

      _logger.i('Image optimization complete!');
    } catch (e) {
      _logger.e('Error optimizing images: $e');
    }
  }

  /// Format file size to human-readable format
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    }
  }

  static Future<void> optimizeImage(String imagePath) async {
    final file = File(imagePath);
    if (!await file.exists()) return;

    final dir = await getTemporaryDirectory();
    final targetPath = path.join(dir.path, '${path.basename(imagePath)}_compressed.png');

    await FlutterImageCompress.compressAndGetFile(
      imagePath,
      targetPath,
      quality: 85,
      format: CompressFormat.png,
    );
  }
}
