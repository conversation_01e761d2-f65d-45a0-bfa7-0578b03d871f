import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tubewell_water_billing/providers/pdf_settings_provider.dart';

import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/screens/settings/pdf_settings_tabs.dart';

class PdfSettingsScreen extends StatefulWidget {
  const PdfSettingsScreen({super.key});

  @override
  State<PdfSettingsScreen> createState() => _PdfSettingsScreenState();
}

class _PdfSettingsScreenState extends State<PdfSettingsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'PDF Settings',
        showPdfOption: false,
      ),
      body: Column(
        children: [
          // Tab bar with fixed height to prevent layout shifts
          PreferredSize(
            preferredSize: const Size.fromHeight(56.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(bottom: BorderSide(color: Colors.grey.shade300, width: 1)),
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Theme.of(context).primaryColor,
                indicatorWeight: 3.0,
                tabs: [
                  Tab(text: 'Templates', icon: Icon(Icons.style)),
                  Tab(text: 'Colors', icon: Icon(Icons.color_lens)),
                  Tab(text: 'Company Info', icon: Icon(Icons.business)),
                ],
              ),
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
          _TemplatesTab(),
          ColorsTab(),
          CompanyInfoTab(),
        ],
            ),
          ),
        ],
      ),
    );
  }
}

class _TemplatesTab extends StatelessWidget {
  const _TemplatesTab();

  @override
  Widget build(BuildContext context) {
    final pdfSettings = Provider.of<PdfSettingsProvider>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Template Presets',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Template presets
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildPresetCard(
                context,
                title: 'Modern',
                description: 'Clean, modern design with cards',
                color: Colors.blue,
                isSelected: pdfSettings.templateType == TemplateType.modern,
                onTap: () => pdfSettings.applyPreset('modern'),
              ),
              _buildPresetCard(
                context,
                title: 'Invoice',
                description: 'Professional invoice layout',
                color: Colors.green,
                isSelected: pdfSettings.templateType == TemplateType.invoice,
                onTap: () => pdfSettings.applyPreset('invoice'),
              ),
              _buildPresetCard(
                context,
                title: 'Report',
                description: 'Standard report format',
                color: Colors.purple,
                isSelected: pdfSettings.templateType == TemplateType.report,
                onTap: () => pdfSettings.applyPreset('report'),
              ),
              _buildPresetCard(
                context,
                title: 'Minimal',
                description: 'Simple, clean design',
                color: Colors.blueGrey,
                isSelected: pdfSettings.templateType == TemplateType.standard &&
                           pdfSettings.headerStyle == HeaderStyle.minimal,
                onTap: () => pdfSettings.applyPreset('minimal'),
              ),
            ],
          ),

          const Divider(height: 32),

          // Advanced settings
          const Text(
            'Advanced Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Template type
          _buildDropdownSetting<TemplateType>(
            context,
            title: 'Template Type',
            value: pdfSettings.templateType,
            items: TemplateType.values.map((type) =>
              DropdownMenuItem(
                value: type,
                child: Text(_getTemplateTypeName(type)),
              )
            ).toList(),
            onChanged: (value) {
              if (value != null) pdfSettings.setTemplateType(value);
            },
          ),

          // Header style
          _buildDropdownSetting<HeaderStyle>(
            context,
            title: 'Header Style',
            value: pdfSettings.headerStyle,
            items: HeaderStyle.values.map((style) =>
              DropdownMenuItem(
                value: style,
                child: Text(_getHeaderStyleName(style)),
              )
            ).toList(),
            onChanged: (value) {
              if (value != null) pdfSettings.setHeaderStyle(value);
            },
          ),

          // Footer style
          _buildDropdownSetting<FooterStyle>(
            context,
            title: 'Footer Style',
            value: pdfSettings.footerStyle,
            items: FooterStyle.values.map((style) =>
              DropdownMenuItem(
                value: style,
                child: Text(_getFooterStyleName(style)),
              )
            ).toList(),
            onChanged: (value) {
              if (value != null) pdfSettings.setFooterStyle(value);
            },
          ),

          // Content style
          _buildDropdownSetting<ContentStyle>(
            context,
            title: 'Content Style',
            value: pdfSettings.contentStyle,
            items: ContentStyle.values.map((style) =>
              DropdownMenuItem(
                value: style,
                child: Text(_getContentStyleName(style)),
              )
            ).toList(),
            onChanged: (value) {
              if (value != null) pdfSettings.setContentStyle(value);
            },
          ),

          const Divider(height: 32),

          // Display options
          const Text(
            'Display Options',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Show logo
          SwitchListTile(
            title: const Text('Show Logo'),
            value: pdfSettings.showLogo,
            onChanged: (value) => pdfSettings.setShowLogo(value),
          ),

          // Show page numbers
          SwitchListTile(
            title: const Text('Show Page Numbers'),
            value: pdfSettings.showPageNumbers,
            onChanged: (value) => pdfSettings.setShowPageNumbers(value),
          ),

          // Show timestamp
          SwitchListTile(
            title: const Text('Show Timestamp'),
            value: pdfSettings.showTimestamp,
            onChanged: (value) => pdfSettings.setShowTimestamp(value),
          ),

          // Show watermark
          SwitchListTile(
            title: const Text('Show Watermark'),
            value: pdfSettings.showWatermark,
            onChanged: (value) => pdfSettings.setShowWatermark(value),
          ),

          // Watermark text
          if (pdfSettings.showWatermark)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Watermark Text',
                  border: OutlineInputBorder(),
                ),
                initialValue: pdfSettings.watermarkText,
                onChanged: (value) => pdfSettings.setWatermarkText(value),
              ),
            ),

          // Footer text
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'Footer Text',
                border: OutlineInputBorder(),
              ),
              initialValue: pdfSettings.footerText,
              onChanged: (value) => pdfSettings.setFooterText(value),
            ),
          ),

          const SizedBox(height: 16),

          // Reset button
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Reset Settings'),
                    content: const Text('Are you sure you want to reset all PDF settings to defaults?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('CANCEL'),
                      ),
                      TextButton(
                        onPressed: () {
                          pdfSettings.resetToDefaults();
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Settings reset to defaults')),
                          );
                        },
                        child: const Text('RESET'),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.restore),
              label: const Text('Reset to Defaults'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetCard(
    BuildContext context, {
    required String title,
    required String description,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: MediaQuery.of(context).size.width / 2 - 24,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Color.fromRGBO(color.r.toInt(), color.g.toInt(), color.b.toInt(), 0.2) : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.style,
                  color: color,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const Spacer(),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: color,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownSetting<T>(
    BuildContext context, {
    required String title,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<T>(
                  value: value,
                  items: items,
                  onChanged: onChanged,
                  isExpanded: true,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getTemplateTypeName(TemplateType type) {
    switch (type) {
      case TemplateType.standard: return 'Standard';
      case TemplateType.invoice: return 'Invoice';
      case TemplateType.report: return 'Report';
      case TemplateType.statement: return 'Statement';
      case TemplateType.modern: return 'Modern';
      case TemplateType.custom: return 'Custom';
    }
  }

  String _getHeaderStyleName(HeaderStyle style) {
    switch (style) {
      case HeaderStyle.standard: return 'Standard';
      case HeaderStyle.compact: return 'Compact';
      case HeaderStyle.detailed: return 'Detailed';
      case HeaderStyle.minimal: return 'Minimal';
      case HeaderStyle.modern: return 'Modern';
      case HeaderStyle.custom: return 'Custom';
    }
  }

  String _getFooterStyleName(FooterStyle style) {
    switch (style) {
      case FooterStyle.standard: return 'Standard';
      case FooterStyle.compact: return 'Compact';
      case FooterStyle.detailed: return 'Detailed';
      case FooterStyle.minimal: return 'Minimal';
      case FooterStyle.modern: return 'Modern';
      case FooterStyle.custom: return 'Custom';
    }
  }

  String _getContentStyleName(ContentStyle style) {
    switch (style) {
      case ContentStyle.standard: return 'Standard';
      case ContentStyle.twoColumn: return 'Two Column';
      case ContentStyle.modern: return 'Modern';
      case ContentStyle.cards: return 'Cards';
      case ContentStyle.grid: return 'Grid';
      default: return 'Unknown';
    }
  }
}
