import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/billing_service.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/widgets/info_chips.dart';
import 'package:tubewell_water_billing/screens/bill_details_screen.dart';
import 'package:tubewell_water_billing/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';

import 'package:intl/intl.dart';
import 'dart:async';

// Sort options for transactions
enum TransactionSortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  statusUnpaidFirst(label: 'Status (Unpaid first)', icon: Icons.priority_high),
  statusPaidFirst(label: 'Status (Paid first)', icon: Icons.check_circle_outline);

  final String label;
  final IconData icon;

  const TransactionSortOption({
    required this.label,
    required this.icon,
  });
}

class CustomerTransactionsTab extends StatefulWidget {
  final Customer customer;
  final VoidCallback onDataChanged;

  const CustomerTransactionsTab({
    super.key,
    required this.customer,
    required this.onDataChanged,
  });

  @override
  State<CustomerTransactionsTab> createState() =>
      CustomerTransactionsTabState();
}

class CustomerTransactionsTabState extends State<CustomerTransactionsTab>
    with AutomaticKeepAliveClientMixin {
  List<Bill> _bills = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();

  // Search state
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // Sort state
  TransactionSortOption _currentSortOption = TransactionSortOption.dateNewest;
  bool _isSortActive = false;

  // Debounce search to reduce unnecessary rebuilds
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // Group bills by date
  Map<String, List<Bill>> _groupedBills = {};
  List<String> _dateKeys = [];

  // For pull-to-refresh coordination
  Completer<void>? _refreshCompleter;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadBills();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      final query = _searchController.text.trim().toLowerCase();
      if (_searchQuery != query) {
        setState(() {
          _searchQuery = query;
        });
        _loadBills();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Only refresh if not already loading
    if (!_isLoading && !_isLoadingMore) {
      _loadBills();
    }
  }

  // Scroll listener for infinite scrolling
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMoreBills();
      }
    }
  }

  // Public method to force a refresh from parent
  void refreshData() {
    if (mounted && !_isLoading) {
      _loadBills();
    }
  }

  // Used by pull-to-refresh
  Future<void> _handleRefresh() {
    _refreshCompleter = Completer<void>();
    refreshData();
    return _refreshCompleter!.future;
  }

  Future<void> _loadBills() async {
    if (!mounted) return;

    // Reset pagination
    _page = 0;
    _hasMoreData = true;

    setState(() {
      _isLoading = true;
    });

    try {
      final bills = await DatabaseService.getBillsFiltered(
        customerId: widget.customer.id,
        offset: _page * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        sortDescending: true,
      );

      if (!mounted) return;
      _processLoadedBills(bills, isFirstPage: true);
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading transactions: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }

    // Complete the refresh indicator if it's active
    _completeRefresh();
  }

  Future<void> _loadMoreBills() async {
    if (!mounted || !_hasMoreData || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _page++;
      final bills = await DatabaseService.getBillsFiltered(
        customerId: widget.customer.id,
        offset: _page * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        sortDescending: true,
      );

      if (!mounted) return;
      _processLoadedBills(bills, isFirstPage: false);
    } catch (e) {
      if (!mounted) return;
      // Revert page increment on error
      _page--;

      setState(() {
        _isLoadingMore = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading more transactions: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _processLoadedBills(List<Bill> bills, {required bool isFirstPage}) {
    if (!mounted) return;

    // Check if we've reached the end
    _hasMoreData = bills.length >= _pageSize;

    // Update the bills list
    if (isFirstPage) {
      _bills = bills;
    } else {
      _bills.addAll(bills);
    }

    // Apply sorting
    _sortBills(_bills);

    // Group bills by date only for date-based sorting
    if (_isDateBasedSort()) {
      _groupedBills = {};
      for (var bill in _bills) {
        final dateKey = DateFormat('yyyy-MM-dd').format(bill.billDate);
        if (!_groupedBills.containsKey(dateKey)) {
          _groupedBills[dateKey] = [];
        }
        _groupedBills[dateKey]!.add(bill);
      }

      // Sort date keys based on sort option
      _dateKeys = _groupedBills.keys.toList();
      if (_currentSortOption == TransactionSortOption.dateNewest) {
        _dateKeys.sort((a, b) => b.compareTo(a));
      } else {
        _dateKeys.sort((a, b) => a.compareTo(b));
      }
    } else {
      // For non-date sorting, create a single group with all bills
      _groupedBills = {'all': _bills};
      _dateKeys = ['all'];
    }

    setState(() {
      _isLoading = false;
      _isLoadingMore = false;
    });

    // Notify parent to refresh summary
    widget.onDataChanged();

    // Complete the refresh indicator if it's active
    _completeRefresh();
  }

  void _completeRefresh() {
    if (_refreshCompleter?.isCompleted == false) {
      _refreshCompleter?.complete();
    }
  }

  // Helper method to check if current sort is date-based
  bool _isDateBasedSort() {
    return _currentSortOption == TransactionSortOption.dateNewest ||
           _currentSortOption == TransactionSortOption.dateOldest;
  }

  // Sort bills based on current sort option
  void _sortBills(List<Bill> bills) {
    switch (_currentSortOption) {
      case TransactionSortOption.dateNewest:
        bills.sort((a, b) => b.billDate.compareTo(a.billDate));
        break;
      case TransactionSortOption.dateOldest:
        bills.sort((a, b) => a.billDate.compareTo(b.billDate));
        break;
      case TransactionSortOption.amountHighest:
        bills.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case TransactionSortOption.amountLowest:
        bills.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case TransactionSortOption.statusUnpaidFirst:
        bills.sort((a, b) {
          // Unpaid first, then partially paid, then paid
          if (a.isPaid == b.isPaid && a.isPartiallyPaid == b.isPartiallyPaid) {
            return b.billDate.compareTo(a.billDate); // Secondary sort by date
          }
          if (!a.isPaid && !a.isPartiallyPaid) return -1; // Unpaid first
          if (!b.isPaid && !b.isPartiallyPaid) return 1;
          if (a.isPartiallyPaid && !b.isPartiallyPaid && !b.isPaid) return 1; // Partial after unpaid
          if (b.isPartiallyPaid && !a.isPartiallyPaid && !a.isPaid) return -1;
          if (a.isPaid) return 1; // Paid last
          if (b.isPaid) return -1;
          return 0;
        });
        break;
      case TransactionSortOption.statusPaidFirst:
        bills.sort((a, b) {
          // Paid first, then partially paid, then unpaid
          if (a.isPaid == b.isPaid && a.isPartiallyPaid == b.isPartiallyPaid) {
            return b.billDate.compareTo(a.billDate); // Secondary sort by date
          }
          if (a.isPaid) return -1; // Paid first
          if (b.isPaid) return 1;
          if (a.isPartiallyPaid && !b.isPartiallyPaid && !b.isPaid) return -1; // Partial before unpaid
          if (b.isPartiallyPaid && !a.isPartiallyPaid && !a.isPaid) return 1;
          if (!a.isPaid && !a.isPartiallyPaid) return 1; // Unpaid last
          if (!b.isPaid && !b.isPartiallyPaid) return -1;
          return 0;
        });
        break;
    }
  }

  // Handle sort option change
  void _onSortChanged(TransactionSortOption option) {
    setState(() {
      _currentSortOption = option;
      _isSortActive = option != TransactionSortOption.dateNewest;
    });

    // Re-process current bills with new sort
    _processLoadedBills(_bills, isFirstPage: true);
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
    });
    _loadBills();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    if (_isLoading && _bills.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_bills.isEmpty) {
      return Column(
        children: [
          // Search and filter bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search transactions...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                  },
                                )
                              : null,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Sort button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: IconButton(
                        icon: Stack(
                          alignment: Alignment.center,
                          children: [
                            const Icon(Icons.sort),
                            if (_isSortActive)
                              Positioned(
                                right: 0,
                                bottom: 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.purple,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        tooltip: 'Sort: ${_currentSortOption.label}',
                        onPressed: _showSortDialog,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: EmptyStateWidget(
              icon: _searchQuery.isNotEmpty
                  ? Icons.search_off
                  : Icons.receipt_long,
              title: _searchQuery.isNotEmpty
                  ? 'No Results Found'
                  : 'No Transactions',
              message: _searchQuery.isNotEmpty
                  ? 'Try different search terms'
                  : 'This customer has no transactions yet.',
              buttonText: _searchQuery.isNotEmpty
                  ? 'Clear Search'
                  : 'Add Transaction',
              onButtonPressed: () {
                if (_searchQuery.isNotEmpty) {
                  _clearSearch();
                } else {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TransactionFormScreen(
                        selectedCustomer: widget.customer,
                      ),
                    ),
                  ).then((result) {
                    if (!mounted) return;
                    if (result == true) {
                      _loadBills();
                    }
                  });
                }
              },
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        // Search and filter bar
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search transactions...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                },
                              )
                            : null,
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Sort button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: IconButton(
                      icon: Stack(
                        alignment: Alignment.center,
                        children: [
                          const Icon(Icons.sort),
                          if (_isSortActive)
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.purple,
                                ),
                              ),
                            ),
                        ],
                      ),
                      tooltip: 'Sort: ${_currentSortOption.label}',
                      onPressed: _showSortDialog,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _handleRefresh,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // Transaction list as a sliver
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      // Show loading indicator at the end
                      if (index == _dateKeys.length) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Center(
                            child: _isLoadingMore
                                ? const CircularProgressIndicator(strokeWidth: 2)
                                : TextButton(
                                    onPressed: _loadMoreBills,
                                    child: Text('Load More',
                                        style: TextStyle(color: Colors.teal.shade700)),
                                  ),
                          ),
                        );
                      }

                      final dateKey = _dateKeys[index];
                      final billsForDate = _groupedBills[dateKey] ?? [];

                      // For non-date sorting, show flat list without date headers
                      if (!_isDateBasedSort()) {
                        return Column(
                          children: billsForDate.map((bill) {
                            final billId = '#${bill.id.toString().padLeft(3, '0')}';
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              child: _buildBillCard(bill, billId),
                            );
                          }).toList(),
                        );
                      }

                      // For date-based sorting, show grouped by date
                      final dateFormatted =
                          DateFormat('EEEE, dd MMMM yyyy').format(DateTime.parse(dateKey));

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date header
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                            child: Container(
                              width: double.infinity,
                              padding:
                                  const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                              decoration: BoxDecoration(
                                color: Colors.teal.shade700,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.calendar_today,
                                      color: Colors.white, size: 18),
                                  const SizedBox(width: 8),
                                  Text(
                                    dateFormatted,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    '${billsForDate.length} ${billsForDate.length == 1 ? 'transaction' : 'transactions'}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Bills for this date
                          ...billsForDate.map((bill) {
                            final billId = '#${bill.id.toString().padLeft(3, '0')}';
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              child: _buildBillCard(bill, billId),
                            );
                          }),
                        ],
                      );
                    },
                    childCount: _dateKeys.length + (_hasMoreData ? 1 : 0),
                  ),
                ),
              ],
            ),
          ), // Closes CustomScrollView
        ), // Closes Expanded
      ], // Closes Column
    ); // Closes return statement
  }

  // Build a bill card widget
  Widget _buildBillCard(Bill bill, String billId) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: bill.isPaid
              ? Colors.green.shade400
              : bill.isPartiallyPaid
                  ? Colors.blue.shade400
                  : Colors.red.shade400,
          width: 2.0,
        ),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => BillDetailsScreen(
                bill: bill,
                customer: widget.customer,
              ),
            ),
          ).then((_) {
            refreshData();
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row 1: Bill ID and Payment Status
              Row(
                children: [
                  // Bill ID with icon
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.indigo.shade50,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.indigo.shade200),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.receipt, size: 14, color: Colors.indigo.shade700),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              "Bill No $billId",
                              style: TextStyle(
                                color: Colors.indigo.shade800,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Status chip
                  Expanded(
                    child: StatusChip(
                      status: bill.isPaid
                          ? 'PAID'
                          : bill.isPartiallyPaid
                              ? 'PARTIAL'
                              : 'UNPAID',
                      isPaid: bill.isPaid,
                      isPartiallyPaid: bill.isPartiallyPaid,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Edit button
                  GestureDetector(
                    onTap: () {
                      NavigationHelper.navigateWithSlide(
                        context,
                        TransactionFormScreen(
                          existingBill: bill,
                          selectedCustomer: widget.customer,
                        ),
                      ).then((result) {
                        if (result == true) {
                          refreshData();
                        }
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: Colors.cyan.shade50,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.cyan.shade200),
                      ),
                      child: Icon(Icons.edit, color: Colors.cyan.shade700, size: 14),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Divider(height: 1, thickness: 1),
              const SizedBox(height: 12),
              // Row 2: Duration and Amount
              Row(
                children: [
                  // Time duration with icon
                  Expanded(
                    child: DurationChip(
                      hours: bill.durationHoursWhole,
                      minutes: bill.durationMinutes,
                      showLabel: false,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Amount with icon
                  Expanded(
                    child: AmountChip(
                      amount: bill.amount,
                      showLabel: false,
                      customColor: bill.isPaid
                          ? Colors.green.shade700
                          : bill.isPartiallyPaid
                              ? Colors.blue.shade700
                              : Colors.red.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Delete button
                  GestureDetector(
                    onTap: () => _showDeleteConfirmation(bill),
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Icon(Icons.delete, color: Colors.red.shade700, size: 16),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show sort options dialog
  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Transactions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TransactionSortOption.values.map((option) {
            final isSelected = option == _currentSortOption;
            return ListTile(
              leading: Icon(
                option.icon,
                color: isSelected ? Colors.purple : null,
              ),
              title: Text(option.label),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.purple)
                  : null,
              selected: isSelected,
              selectedColor: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _onSortChanged(option);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(Bill bill) async {
    // Check if the bill has linked payments
    final payments = await DatabaseService.getPaymentsByBill(bill.id);

    // Check if widget is still mounted after async operation
    if (!mounted) return;

    final hasLinkedPayments = payments.isNotEmpty;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title:
            Text('Delete Bill', style: TextStyle(color: Colors.red.shade700)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to delete this bill?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // Bill details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Bill #${bill.id}'),
                  Text('Amount: Rs. ${bill.amount.toStringAsFixed(2)}'),
                  Text(
                      'Duration: ${bill.durationHoursWhole}h ${bill.durationMinutes}m'),
                  Text(
                      'Status: ${bill.isPaid ? 'Paid' : (bill.isPartiallyPaid ? 'Partially Paid' : 'Unpaid')}'),
                ],
              ),
            ),

            if (hasLinkedPayments) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning,
                            color: Colors.orange.shade700, size: 20),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'This bill has linked payments',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Deleting this bill will also remove ${payments.length} linked payment(s). This action cannot be undone.',
                      style: TextStyle(color: Colors.orange.shade800),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              NavigationHelper.goBack(context);
              _deleteBill(bill);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteBill(Bill bill) async {
    try {
      // Use the BillingService to safely delete the bill and handle linked payments
      final success = await BillingService.deleteBillSafely(bill.id);

      // Check if widget is still mounted after async operation
      if (!mounted) return;

      if (!success) {
        throw Exception('Failed to delete bill');
      }

      // Refresh the transactions list
      refreshData();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bill deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Check if widget is still mounted
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete bill: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
