import 'dart:io';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';

/// Utility service for PDF operations like opening and saving
class PdfUtilityService {
  /// Open a PDF file
  static Future<void> openPdf(File file) async {
    try {

      final exists = await file.exists();

      if (!exists) {
        throw Exception('PDF file does not exist at path: ${file.path}');
      }

      // Ensure file path uses correct separators for the platform
      final filePath = file.path.replaceAll(r'\', '/');

      // On Android, we need to ensure the file has the correct mime type
      if (Platform.isAndroid) {

        // Try multiple approaches to open the file
        try {
          // First approach: Use OpenFile package
          final result = await OpenFile.open(
            filePath,
            type: 'application/pdf',
          );


          if (result.type != ResultType.done) {
                        throw Exception('Primary method failed: ${result.message}');
          }
          return;
        } catch (e) {
                    // Continue to fallback methods
        }

        // Second approach: Try using Share.shareFiles which often works when OpenFile fails
        try {
          await Share.shareXFiles(
            [XFile(filePath)],
            subject: 'Water Bill PDF',
          );
                    return;
        } catch (e) {
                    // Fall through to final approach
        }
      }

      // Default approach for non-Android or as final fallback
      final result = await OpenFile.open(filePath);

      if (result.type != ResultType.done) {
        throw Exception('Could not open the file: ${result.message}');
      }
    } catch (e) {
                  rethrow;
    }
  }

  /// Save PDF to downloads folder
  static Future<String> savePdfToDownloads(File pdfFile) async {
    try {
      // Get the downloads directory
      Directory? downloadsDirectory;

      if (Platform.isAndroid) {
        // For Android, we use the Downloads directory
        downloadsDirectory = Directory('/storage/emulated/0/Download');
        if (!await downloadsDirectory.exists()) {
          // Fallback to application documents directory
          final appDocDir = await getApplicationDocumentsDirectory();
          downloadsDirectory = Directory('${appDocDir.path}/Downloads');
          if (!await downloadsDirectory.exists()) {
            await downloadsDirectory.create(recursive: true);
          }
        }
      } else if (Platform.isIOS) {
        // iOS doesn't have a Downloads folder, so we use Documents
        final appDocDir = await getApplicationDocumentsDirectory();
        downloadsDirectory = appDocDir;
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        // For desktop platforms
        downloadsDirectory = await getDownloadsDirectory();
        if (downloadsDirectory == null) {
          final appDocDir = await getApplicationDocumentsDirectory();
          downloadsDirectory = Directory('${appDocDir.path}/Downloads');
          if (!await downloadsDirectory.exists()) {
            await downloadsDirectory.create(recursive: true);
          }
        }
      } else {
        // Fallback to app documents directory for other platforms
        final appDocDir = await getApplicationDocumentsDirectory();
        downloadsDirectory = appDocDir;
      }

      // Create a unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final originalFileName = pdfFile.path.split('/').last;
      final newFileName = 'water_bill_${timestamp}_$originalFileName';
      final savePath = '${downloadsDirectory.path}/$newFileName';

      // Copy the file to the downloads directory
      final savedFile = await pdfFile.copy(savePath);

      return savedFile.path;
    } catch (e) {
      rethrow;
    }
  }

  /// Load built-in fonts for PDF - using built-in fonts for better compatibility
  static Future<Map<String, pw.Font>> loadPdfFonts() async {
    try {
      // Use built-in fonts for better compatibility and smaller app size
      return {
        'regular': pw.Font.courier(),
        'bold': pw.Font.courierBold(),
        'italic': pw.Font.courierOblique(),
      };
    } catch (e) {
      // Fallback to basic fonts if loading fails
      return {
        'regular': pw.Font.helvetica(),
        'bold': pw.Font.helveticaBold(),
        'italic': pw.Font.helveticaOblique(),
      };
    }
  }

  /// Save PDF document to a temporary file with a given name
  static Future<File> savePdfToTemp(pw.Document pdf, String fileName) async {
    try {
      // Debug print to help identify issues

      final output = await getTemporaryDirectory();

      // Check if directory exists
      if (!await output.exists()) {
                await output.create(recursive: true);
      }

      final file = File('${output.path}/$fileName');

      // Save the PDF
      final bytes = await pdf.save();

      await file.writeAsBytes(bytes);

      // Verify file exists
      await file.exists();

      return file;
    } catch (e) {
                  rethrow;
    }
  }



  /// Share PDF file
  static Future<void> sharePdf(File pdfFile, ScaffoldMessengerState scaffoldMessenger) async {
    try {
      final fileName = pdfFile.path.split('/').last;

      // Ensure the file exists before sharing
      if (!await pdfFile.exists()) {
                throw Exception('PDF file not found for sharing');
      }

      // Get normalized path
      final filePath = pdfFile.path.replaceAll(r'\', '/');

      if (Platform.isAndroid) {
                // On Android, specify the mime type
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'Water Bill PDF',
          subject: 'Water Bill - $fileName',
        );
      } else {
        // Default sharing for other platforms
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'Sharing PDF file: $fileName',
          subject: fileName,
        );
      }

            scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('PDF shared successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error sharing PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
      rethrow;
    }
  }

  /// Request storage permissions
  static Future<bool> requestStoragePermission() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
                final status = await Permission.storage.request();

        // On Android 11+, we also need to request manage external storage for some operations
        if (Platform.isAndroid) {
          final sdkVersion = int.tryParse(Platform.operatingSystemVersion.split(' ').first) ?? 0;
          if (sdkVersion >= 30) { // Android 11+
                        await Permission.manageExternalStorage.request();
                        // Still return the basic storage permission result as it's sufficient for most cases
          }
        }

        return status.isGranted;
      }
            return true; // On desktop platforms, assume permission is granted
    } catch (e) {
            return false;
    }
  }

  /// Get a formatted file name with timestamp
  static String getFormattedFileName(String prefix, {String? suffix}) {
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
    return '${prefix}_$timestamp${suffix ?? ''}.pdf';
  }

  /// Create a PDF with a watermark
  static Future<pw.Document> addWatermark(pw.Document document, String watermarkText) async {
    // Create a new document
    final newDocument = pw.Document();

    // Add a single page with watermark
    newDocument.addPage(
      pw.Page(
        build: (context) {
          return pw.Stack(
            children: [
              // Watermark
              pw.Center(
                child: pw.Transform.rotate(
                  angle: -0.5,
                  child: pw.Text(
                    watermarkText,
                    style: pw.TextStyle(
                      color: PdfColor.fromHex('#DDDDDD'),
                      fontSize: 60,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );

    return newDocument;
  }

  /// Create a PDF with a logo
  static Future<pw.MemoryImage?> getLogoImage() async {
    try {
      final ByteData data = await rootBundle.load('assets/images/logo.png');
      return pw.MemoryImage(data.buffer.asUint8List());
    } catch (e) {
      return null;
    }
  }

  /// Get the app's document directory
  static Future<Directory> getAppDocDirectory() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        return await getApplicationDocumentsDirectory();
      } else {
        // For desktop platforms
        final tempDir = await getTemporaryDirectory();
        final appDocDir = Directory('${tempDir.path}/tubewell_water_billing');
        if (!await appDocDir.exists()) {
          await appDocDir.create(recursive: true);
        }
        return appDocDir;
      }
    } catch (e) {
      // Fallback to temp directory
      return await getTemporaryDirectory();
    }
  }

  /// Create a PDF with a QR code
  static Future<pw.Widget> buildQrCode(String data, {double size = 100}) async {
    try {
      return pw.BarcodeWidget(
        barcode: pw.Barcode.qrCode(),
        data: data,
        width: size,
        height: size,
      );
    } catch (e) {
      return pw.Container(
        width: size,
        height: size,
        child: pw.Center(
          child: pw.Text('QR Error'),
        ),
      );
    }
  }
}

