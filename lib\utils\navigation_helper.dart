import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tubewell_water_billing/utils/page_transitions.dart';

/// A helper class for standardized navigation throughout the app
/// This ensures consistent transitions and prevents screen vibration
class NavigationHelper {
  /// Navigate to a new screen with a smooth fade transition
  /// This is the preferred method for most navigation in the app
  static Future<T?> navigateTo<T>(
    BuildContext context,
    Widget screen, {
    bool replace = false,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
  }) {
    if (replace) {
      return Navigator.of(context).pushReplacementFade<T, dynamic>(
        screen,
        duration: duration,
        curve: curve,
      );
    } else {
      return Navigator.of(context).pushFade<T>(
        screen,
        duration: duration,
        curve: curve,
      );
    }
  }

  /// Navigate to a new screen with a slide transition
  /// Use this for hierarchical navigation (e.g., detail screens)
  static Future<T?> navigateWithSlide<T>(
    BuildContext context,
    Widget screen, {
    bool replace = false,
    SlideDirection direction = SlideDirection.right,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
    double slideOffset = 0.1, // Further reduced for smoother transitions
  }) {
    if (replace) {
      return Navigator.of(context).pushReplacementSlide<T, dynamic>(
        screen,
        direction: direction,
        duration: duration,
        curve: curve,
        slideOffset: slideOffset,
      );
    } else {
      return Navigator.of(context).pushSlide<T>(
        screen,
        direction: direction,
        duration: duration,
        curve: curve,
        slideOffset: slideOffset,
      );
    }
  }

  /// Pop the current screen with a smooth fade transition
  static void goBack<T>(BuildContext context, [T? result]) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop(result);
    }
  }

  /// Pop the current screen with a smooth fade transition
  static void goBackWithFade(BuildContext context, {
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
  }) {
    Navigator.of(context).popWithFade(
      duration: duration,
      curve: curve,
    );
  }

  /// Show a material route with default settings
  /// Use this only when you need a standard Material transition
  static Future<T?> showMaterialRoute<T>(
    BuildContext context,
    Widget screen, {
    bool fullscreenDialog = false,
  }) {
    return Navigator.of(context).push<T>(
      MaterialPageRoute(
        builder: (_) => screen,
        fullscreenDialog: fullscreenDialog,
      ),
    );
  }

  /// Exit the app completely
  /// This is the proper way to exit a Flutter app
  static void exitApp() {
    SystemNavigator.pop();
  }

  /// Show exit confirmation dialog and exit if confirmed
  static Future<void> showExitConfirmation(BuildContext context) async {
    final shouldExit = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit App'),
        content: const Text('Are you sure you want to exit the app?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Yes'),
          ),
        ],
      ),
    ) ?? false;

    if (shouldExit) {
      exitApp();
    }
  }
}
