import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/services/expense_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';

class ExpenseFormScreen extends StatefulWidget {
  final Expense? existingExpense;

  const ExpenseFormScreen({
    super.key,
    this.existingExpense,
  });

  @override
  State<ExpenseFormScreen> createState() => _ExpenseFormScreenState();
}

class _ExpenseFormScreenState extends State<ExpenseFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _remarksController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  String _selectedCategory = 'Other';
  String _selectedPaymentMethod = 'Cash';
  bool _isLoading = false;
  bool _isEditing = false;

  final List<String> _categories = ExpenseService.getPredefinedCategories();
  final List<String> _paymentMethods = [
    'Cash',
    'Bank Transfer',
    'Check',
    'Credit Card',
    'Other'
  ];

  // Payment method icons map
  final Map<String, IconData> _paymentMethodIcons = {
    'Cash': Icons.money,
    'Bank Transfer': Icons.account_balance,
    'Check': Icons.receipt_long,
    'Credit Card': Icons.credit_card,
    'Other': Icons.payments,
  };

  // Payment method colors - ensuring no orange, yellow, grey, and no duplicates
  final Map<String, Color> _paymentMethodColors = {
    'Cash': const Color(0xFF00897B), // Teal
    'Bank Transfer': const Color(0xFF1E88E5), // Blue
    'Check': const Color(0xFF8E24AA), // Purple
    'Credit Card': const Color(0xFFD81B60), // Pink
    'Other': const Color(0xFF3949AB), // Indigo
  };

  // Category icons map
  final Map<String, IconData> _categoryIcons = {
    'Electricity': Icons.electric_bolt,
    'Maintenance': Icons.handyman,
    'Repairs': Icons.build,
    'Fuel': Icons.local_gas_station,
    'Salaries': Icons.payments,
    'Equipment': Icons.construction,
    'Rent': Icons.home,
    'Taxes': Icons.receipt_long,
    'Transportation': Icons.directions_car,
    'Office Supplies': Icons.business_center,
    'Other': Icons.category,
  };

  // Category colors - ensuring no orange, yellow, grey, and no duplicates
  final Map<String, Color> _categoryColors = {
    'Electricity': const Color(0xFF1E88E5), // Blue
    'Maintenance': const Color(0xFF43A047), // Green
    'Repairs': const Color(0xFF5E35B1), // Deep Purple
    'Fuel': const Color(0xFFD81B60), // Pink
    'Salaries': const Color(0xFF00897B), // Teal
    'Equipment': const Color(0xFF8E24AA), // Purple
    'Rent': const Color(0xFF3949AB), // Indigo
    'Taxes': const Color(0xFF00ACC1), // Cyan
    'Transportation': const Color(0xFF7CB342), // Light Green
    'Office Supplies': const Color(0xFF0D47A1), // Dark Blue (replacing Brown)
    'Other': const Color(0xFF4A148C), // Dark Purple (replacing Blue Grey)
  };

  // Date formatter
  final _dateFormat = DateFormat('MMM d, yyyy');

  @override
  void initState() {
    super.initState();
    _isEditing = widget.existingExpense != null;

    if (_isEditing) {
      // Populate form with existing expense data
      _amountController.text = widget.existingExpense!.amount.toString();
      _remarksController.text = widget.existingExpense!.remarks ?? '';
      _selectedDate = widget.existingExpense!.date;
      _selectedCategory = widget.existingExpense!.category;
      _selectedPaymentMethod = widget.existingExpense!.paymentMethod ?? 'Cash';
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF2E7D32),
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text.trim());
      final remarks = _remarksController.text.trim();
      // Use category as the description since we removed the description field
      final description = _selectedCategory;

      Expense expense;
      if (_isEditing) {
        // Update existing expense
        expense = widget.existingExpense!.clone();
        expense.description = description;
        expense.amount = amount;
        expense.date = _selectedDate;
        expense.category = _selectedCategory;
        expense.paymentMethod = _selectedPaymentMethod;
        expense.remarks = remarks.isNotEmpty ? remarks : null;

        await ExpenseService.updateExpense(expense);
      } else {
        // Create new expense
        expense = Expense(
          description: description,
          amount: amount,
          date: _selectedDate,
          category: _selectedCategory,
          paymentMethod: _selectedPaymentMethod,
          remarks: remarks.isNotEmpty ? remarks : null,
        );

        await ExpenseService.saveExpense(expense);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Expense saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        NavigationHelper.goBack(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving expense: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TubewellAppBar(
        title: _isEditing ? 'Edit Expense' : 'Add Expense',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Title
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.green.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _isEditing ? Icons.edit_document : Icons.add_circle,
                            color: Colors.green.shade700,
                            size: 28,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            _isEditing ? 'Edit Expense' : 'Add New Expense',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Date picker
                    InkWell(
                      onTap: () => _selectDate(context),
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Date',
                          labelStyle: TextStyle(color: Colors.purple.shade700),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide:
                                BorderSide(color: Colors.indigo.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                                color: Colors.purple.shade700, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.purple.shade50,
                          prefixIcon: Icon(Icons.calendar_today,
                              color: Colors.purple.shade700),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _dateFormat.format(_selectedDate),
                              style: const TextStyle(fontSize: 16),
                            ),
                            Icon(Icons.arrow_drop_down,
                                color: Colors.purple.shade700),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Amount field
                    TextFormField(
                      controller: _amountController,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      decoration: InputDecoration(
                        labelText: 'Amount',
                        labelStyle: TextStyle(color: Colors.green.shade700),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.green.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                              color: Colors.green.shade700, width: 2),
                        ),
                        filled: true,
                        fillColor: Colors.green.shade50,
                        prefixIcon: Icon(Icons.attach_money,
                            color: Colors.green.shade700),
                        prefixText:
                            '${CurrencyService.currentCurrency.symbol} ',
                        prefixStyle: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter an amount';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        if (double.parse(value) <= 0) {
                          return 'Amount must be greater than zero';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Category dropdown
                    DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      decoration: InputDecoration(
                        labelText: 'Category',
                        labelStyle: TextStyle(
                          color: _categoryColors[_selectedCategory] ??
                              const Color(0xFF3949AB),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                              color: _categoryColors[_selectedCategory] ??
                                  Colors.indigo.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: _categoryColors[_selectedCategory] ??
                                const Color(0xFF3949AB),
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: (_categoryColors[_selectedCategory] ??
                                const Color(0xFF3949AB))
                            .withAlpha(
                                26), // 0.1 opacity is approximately alpha 26
                      ),
                      items: _categories.map((category) {
                        return DropdownMenuItem<String>(
                          value: category,
                          child: Row(
                            children: [
                              Icon(
                                _categoryIcons[category] ?? Icons.category,
                                color: _categoryColors[category] ??
                                    const Color(0xFF4A148C),
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Text(category),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedCategory = value;
                          });
                        }
                      },
                      dropdownColor: Colors.white,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: _categoryColors[_selectedCategory] ??
                            const Color(0xFF3949AB),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Payment method dropdown
                    DropdownButtonFormField<String>(
                      value: _selectedPaymentMethod,
                      decoration: InputDecoration(
                        labelText: 'Payment Method',
                        labelStyle: TextStyle(
                          color: _paymentMethodColors[_selectedPaymentMethod] ??
                              const Color(0xFF00897B),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                              color: _paymentMethodColors[
                                      _selectedPaymentMethod] ??
                                  Colors.teal.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color:
                                _paymentMethodColors[_selectedPaymentMethod] ??
                                    const Color(0xFF00897B),
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: (_paymentMethodColors[
                                    _selectedPaymentMethod] ??
                                const Color(0xFF00897B))
                            .withAlpha(
                                26), // 0.1 opacity is approximately alpha 26
                      ),
                      items: _paymentMethods.map((method) {
                        return DropdownMenuItem<String>(
                          value: method,
                          child: Row(
                            children: [
                              Icon(
                                _paymentMethodIcons[method] ?? Icons.payment,
                                color: _paymentMethodColors[method] ??
                                    const Color(0xFF00897B),
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Text(method),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedPaymentMethod = value;
                          });
                        }
                      },
                      dropdownColor: Colors.white,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: _paymentMethodColors[_selectedPaymentMethod] ??
                            const Color(0xFF00897B),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Remarks field (Optional Notes)
                    TextFormField(
                      controller: _remarksController,
                      decoration: InputDecoration(
                        labelText: 'Notes (Optional)',
                        labelStyle: TextStyle(color: Colors.indigo.shade700),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.indigo.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                              color: Colors.indigo.shade700, width: 2),
                        ),
                        filled: true,
                        fillColor: Colors.indigo.shade50,
                        prefixIcon:
                            Icon(Icons.note, color: Colors.indigo.shade700),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),

                    // Save button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveExpense,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2E7D32),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 4,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.save, color: Colors.white),
                            const SizedBox(width: 8),
                            Text(
                              _isEditing ? 'Update Expense' : 'Save Expense',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
