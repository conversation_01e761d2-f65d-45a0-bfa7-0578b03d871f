import 'dart:io';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_utility_service.dart';

/// Service for generating payment PDF reports
class PdfPaymentsService {
  /// Generate a payment PDF report
  static Future<File> generatePaymentsPdf({
    Customer? customer,
    List<Payment>? payments,
    DateTime? startDate,
    DateTime? endDate,
    PdfSettings? settings,
    bool useModernTable = true,
  }) async {
    try {
      // Use provided settings or create default modern settings
      final pdfSettings = settings ?? PdfSettings.modern().copyWith(
        primaryColor: PdfColor.fromHex('#2E7D32'), // Green
        accentColor: PdfColor.fromHex('#1565C0'), // Blue
        additionalSettings: {
          'footerText': 'Payment Report',
        },
      );

      // Define colors from settings
      final primaryColor = pdfSettings.primaryColor;
      final accentColor = pdfSettings.accentColor;
      final textColor = pdfSettings.textColor;
      final lightGreen = PdfBaseService.lightGreen;
      final lightBlue = PdfBaseService.lightBlue;

      // Load fonts
      await PdfBaseService.loadFonts();
      final boldFont = PdfBaseService.boldFont;
      final regularFont = PdfBaseService.regularFont;
      final italicFont = PdfBaseService.italicFont;

      // Get data if not provided
      payments ??= customer != null
          ? await DatabaseService.getPaymentsByCustomer(customer.id)
          : await DatabaseService.getAllPayments();

      // Apply date filters if provided
      if (startDate != null || endDate != null) {
        payments = payments.where((payment) {
          if (startDate != null && payment.paymentDate.isBefore(startDate)) {
            return false;
          }
          if (endDate != null) {
            final endOfDay =
                DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
            if (payment.paymentDate.isAfter(endOfDay)) {
              return false;
            }
          }
          return true;
        }).toList();
      }

      // Sort payments by date (newest first)
      payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));

      // Get customer map for customer names
      Map<int, Customer> customersMap = {};
      if (customer == null) {
        final customers = await DatabaseService.getAllCustomers();
        customersMap = {for (var c in customers) c.id: c};
      }

      // Calculate totals
      double totalAmount = 0;
      for (var payment in payments) {
        totalAmount += payment.amount;
      }

      // Create a PDF document with template
      final pdf = await PdfTemplateService.createPdfWithTemplate(
        title: 'PAYMENT REPORT',
        subtitle: customer != null ? '${customer.name} Payments' : 'All Payments',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        watermarkText: pdfSettings.showWatermark ? (pdfSettings.watermarkText ?? 'COPY') : null,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      // Create content widgets
      final List<pw.Widget> contentWidgets = [
        // Date range if provided
        if (startDate != null || endDate != null)
          pw.Text(
              'Period: ${startDate != null ? DateFormat('dd/MM/yyyy').format(startDate) : 'Start'} - ${endDate != null ? DateFormat('dd/MM/yyyy').format(endDate) : 'End'}',
              style: pw.TextStyle(font: regularFont, fontSize: 10)),
        pw.SizedBox(height: 20),

        // Report Summary
        pw.Container(
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: lightGreen,
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('PAYMENT SUMMARY',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: primaryColor,
                  )),
              pw.SizedBox(height: 10),
              pw.Row(
                children: [
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text('Total Payments:',
                            style: pw.TextStyle(font: boldFont, fontSize: 11)),
                        pw.Text('${payments.length}',
                            style: pw.TextStyle(font: boldFont, fontSize: 16)),
                      ],
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text('Total Amount:',
                            style: pw.TextStyle(font: boldFont, fontSize: 11)),
                        pw.Text('Rs. ${totalAmount.toStringAsFixed(2)}',
                            style: pw.TextStyle(
                                font: boldFont,
                                fontSize: 16,
                                color: primaryColor)),
                      ],
                    ),
                  ),
                ],
              ),
              if (customer != null) ...[
                pw.SizedBox(height: 10),
                pw.Text('Customer: ${customer.name}',
                    style: pw.TextStyle(font: boldFont, fontSize: 12)),
                if (customer.contactNumber != null)
                  pw.Text('Phone: ${customer.contactNumber}',
                      style: pw.TextStyle(font: regularFont, fontSize: 10)),
              ],
            ],
          ),
        ),
        pw.SizedBox(height: 20),

        // Payments Table Section
        PdfTemplateService.createSectionTitle(
          'PAYMENT DETAILS',
          color: primaryColor,
        ),

        // Check if payments exist
        if (payments.isEmpty)
          pw.Container(
            padding: const pw.EdgeInsets.all(20),
            decoration: pw.BoxDecoration(
              color: lightBlue,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
            ),
            child: pw.Center(
              child: pw.Text('No payments found for the selected criteria',
                  style: pw.TextStyle(font: italicFont, fontSize: 12)),
            ),
          )
        else if (useModernTable || pdfSettings.contentStyle == ContentStyle.modern ||
                 pdfSettings.contentStyle == ContentStyle.cards)
          // Use modern table for better styling
          PdfTemplateService.createModernDataTable(
            headers: [
              'ID',
              'Date',
              customer == null ? 'Customer/Remarks' : 'Remarks',
              'Method',
              'Amount'
            ],
            data: payments.map((payment) {
              String customerInfo = '';
              if (customer == null) {
                customerInfo = customersMap[payment.customerId]?.name ?? 'Unknown';
              }

              String remarks = '';
              if (payment.remarks != null && payment.remarks!.isNotEmpty) {
                remarks = payment.remarks!;
              }

              String billInfo = '';
              if (payment.billId > 0) {
                billInfo = 'For Bill #${payment.billId}';
              } else if (payment.billId == 0) {
                billInfo = 'Credit Payment';
              }

              return [
                '#${payment.id}',
                '${DateFormat('dd/MM/yyyy').format(payment.paymentDate)}\n${DateFormat('HH:mm').format(payment.paymentDate)}',
                '$customerInfo${customerInfo.isNotEmpty ? '\n' : ''}$remarks${remarks.isNotEmpty ? '\n' : ''}$billInfo',
                payment.paymentMethod ?? 'N/A',
                'Rs. ${payment.amount.toStringAsFixed(2)}'
              ];
            }).toList(),
            headerColor: primaryColor,
            textColor: textColor,
            alternateColor: PdfBaseService.colorWithOpacity(lightGreen, 0.3),
            zebra: true,
            borderRadius: 8,
          )
        else
          // Use standard table for backward compatibility
          PdfTemplateService.createDataTable(
            headers: [
              'ID',
              'Date',
              customer == null ? 'Customer/Remarks' : 'Remarks',
              'Method',
              'Amount'
            ],
            data: payments.map((payment) {
              String customerInfo = '';
              if (customer == null) {
                customerInfo = customersMap[payment.customerId]?.name ?? 'Unknown';
              }

              String remarks = '';
              if (payment.remarks != null && payment.remarks!.isNotEmpty) {
                remarks = payment.remarks!;
              }

              String billInfo = '';
              if (payment.billId > 0) {
                billInfo = 'For Bill #${payment.billId}';
              } else if (payment.billId == 0) {
                billInfo = 'Credit Payment';
              }

              return [
                '#${payment.id}',
                '${DateFormat('dd/MM/yyyy').format(payment.paymentDate)}\n${DateFormat('HH:mm').format(payment.paymentDate)}',
                '$customerInfo${customerInfo.isNotEmpty ? '\n' : ''}$remarks${remarks.isNotEmpty ? '\n' : ''}$billInfo',
                payment.paymentMethod ?? 'N/A',
                'Rs. ${payment.amount.toStringAsFixed(2)}'
              ];
            }).toList(),
            headerColor: lightGreen,
            alternateColor: lightBlue,
            zebra: true,
          ),
      ];

      // Add page with template using settings
      await PdfTemplateService.addPageWithTemplate(
        pdf: pdf,
        contentWidgets: PdfTemplateService.createContentSection(
          contentStyle: pdfSettings.contentStyle,
          contentWidgets: contentWidgets,
          primaryColor: primaryColor,
          accentColor: accentColor,
          textColor: textColor,
        ),
        title: 'PAYMENT REPORT',
        subtitle: customer != null ? '${customer.name} Payments' : 'All Payments',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        contentStyle: pdfSettings.contentStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      // Save the PDF file
      final fileName = customer != null
          ? PdfUtilityService.getFormattedFileName('payments_${customer.id}')
          : PdfUtilityService.getFormattedFileName('all_payments');
      return await PdfUtilityService.savePdfToTemp(pdf, fileName);
    } catch (e) {
      rethrow;
    }
  }
}
