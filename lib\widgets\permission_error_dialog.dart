import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/services/permission_service.dart';

/// A dialog that shows permission errors and provides a way to request permissions
/// directly from within the app without going to settings.
class PermissionErrorDialog extends StatefulWidget {
  final String title;
  final String message;
  final String permissionType;
  final VoidCallback onPermissionGranted;
  final VoidCallback onCancel;

  const PermissionErrorDialog({
    super.key,
    this.title = 'Permission Required',
    required this.message,
    this.permissionType = 'storage',
    required this.onPermissionGranted,
    required this.onCancel,
  });

  @override
  State<PermissionErrorDialog> createState() => _PermissionErrorDialogState();
}

class _PermissionErrorDialogState extends State<PermissionErrorDialog> {
  bool _isRequestingPermission = false;

  Future<void> _requestPermission() async {
    setState(() {
      _isRequestingPermission = true;
    });

    try {
      bool permissionGranted = false;

      if (widget.permissionType == 'storage') {
        // Try multiple approaches to get storage permission
        // First try the standard approach
        permissionGranted = await PermissionService.requestStoragePermission(context);

        // If that didn't work, check if we at least have basic storage permission
        if (!permissionGranted && mounted) {
          final status = await PermissionService.checkStoragePermissionStatus();
          permissionGranted = status;
        }
      } else if (widget.permissionType == 'camera') {
        permissionGranted = await PermissionService.requestCameraPermission(context);
      } else if (widget.permissionType == 'notification') {
        permissionGranted = await PermissionService.requestNotificationPermission(context);
      } else if (widget.permissionType == 'all') {
        // Request all essential permissions
        permissionGranted = await PermissionService.requestAllEssentialPermissions(context);
      }

      if (mounted) {
        if (permissionGranted) {
          Navigator.of(context).pop();
          widget.onPermissionGranted();
        } else {
          setState(() {
            _isRequestingPermission = false;
          });

          // Show a message that permission was denied
          if (mounted) {
            // Show a more detailed message for storage permissions
            if (widget.permissionType == 'storage') {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Storage permission denied. The app will try to work with limited permissions, but some features may not work properly.',
                  ),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 5),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Permission denied. Some features may not work properly.'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRequestingPermission = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permission: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Choose the appropriate icon based on permission type
    IconData iconData;
    Color iconColor = Colors.orange;

    switch (widget.permissionType) {
      case 'storage':
        iconData = Icons.folder_open;
        break;
      case 'camera':
        iconData = Icons.camera_alt;
        break;
      case 'notification':
        iconData = Icons.notifications;
        break;
      case 'all':
        iconData = Icons.security;
        iconColor = Colors.blue;
        break;
      default:
        iconData = Icons.security;
    }

    return AlertDialog(
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            iconData,
            size: 48,
            color: iconColor,
          ),
          const SizedBox(height: 16),
          Text(widget.message),
          const SizedBox(height: 8),
          const Text(
            'This permission is required for this feature to work properly.',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isRequestingPermission ? null : () {
            Navigator.of(context).pop();
            widget.onCancel();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isRequestingPermission ? null : _requestPermission,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
          child: _isRequestingPermission
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : const Text('Grant Permission'),
        ),
      ],
    );
  }
}
