import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/currency.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing/forms/auto_payment_form_screen.dart';
import 'dart:async';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/widgets/reminder_dialog.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';
import 'package:pdf/pdf.dart';

// Import the separated tab screens
import 'package:tubewell_water_billing/screens/customer_detail/summary_tab.dart';
import 'package:tubewell_water_billing/screens/customer_detail/transactions_tab.dart';
import 'package:tubewell_water_billing/screens/customer_detail/payments_tab.dart';

class CustomerDetailScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailScreen({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Summary variables
  bool _isLoadingSummary = true;
  double _totalBilledAmount = 0;
  double _totalPaidAmount = 0;
  double _netBalance = 0;

  // Create keys for each tab to directly access their state
  final GlobalKey<CustomerTransactionsTabState> _transactionsTabKey =
      GlobalKey();
  final GlobalKey<CustomerPaymentsTabState> _paymentsTabKey = GlobalKey();
  final GlobalKey<CustomerSummaryTabState> _summaryTabKey = GlobalKey();

  // Stream subscription for currency changes
  late final StreamSubscription<Currency> _currencySubscription;

  // Data change subscription
  late final StreamSubscription<DataChangeType> _dataChangeSubscription;

  // Store the tab controller listener function
  void _handleTabChange() {
    // Update tab colors when tab changes
    if (!_tabController.indexIsChanging && mounted) {
      setState(() {
        // This will trigger a rebuild to update tab colors
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Add listener to update UI when tab changes
    _tabController.addListener(_handleTabChange);
    _loadCustomerSummary();

    // Listen for currency changes
    _currencySubscription = CurrencyService.onCurrencyChanged.listen((_) {
      // When currency changes, refresh the UI
      if (mounted) {
        setState(() {
          // Just trigger a rebuild to update currency formatting
        });
      }
    });

    // Listen for data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      // Refresh data when relevant changes occur
      if (changeType == DataChangeType.payment ||
          changeType == DataChangeType.bill ||
          changeType == DataChangeType.customer ||
          changeType == DataChangeType.all) {
        if (mounted) {
          debugPrint(
              'CustomerDetailScreen: Refreshing due to ${changeType.toString()} change');
          _refreshAllTabs();
          _loadCustomerSummary();
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _currencySubscription.cancel();
    _dataChangeSubscription.cancel();
    super.dispose();
  }

  // Custom method to handle PDF generation based on the active tab
  Future<void> _handlePdfGeneration(BuildContext context) async {
    // Determine which type of PDF to generate based on active tab
    // 0 = Summary, 1 = Transactions, 2 = Payments

    try {
      // Get the data based on the active tab
      if (_tabController.index == 0) {
        // Summary tab - generate customer summary PDF
        // Get bills for this customer
        final bills = await DatabaseService.getBillsByCustomer(widget.customer.id);

        // Create PDF data for customer summary
        final pdfData = {
          'customerDetail': true,
          'customer': widget.customer,
          'bills': bills,
          'summary': {
            'totalBilled': _totalBilledAmount,
            'totalPaid': _totalPaidAmount,
            'netBalance': _netBalance,
          },
        };

        // Check if widget is still mounted before proceeding
        if (!mounted) return;

        // Store a local copy of the PDF data and settings
        final localPdfData = pdfData;
        final pdfSettings = PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': '${widget.customer.name} - Customer Summary',
          },
        );

        // Capture the context before the async gap
        final capturedContext = context;

        // Use the universal PDF service to generate the PDF with modern design
        if (mounted) {
          // Create a local function to handle PDF generation
          Future<void> generatePdf() async {
            await UniversalPdfService.handlePdf(
              capturedContext,
              localPdfData,
              autoOpen: true,
              showSaveOption: true,
              showShareOption: true,
              pdfSettings: pdfSettings,
            );
          }

          // Call the local function
          await generatePdf();
        }
      } else if (_tabController.index == 1) {
        // Transactions tab - generate transaction statement
        // Get bills for this customer
        final bills = await DatabaseService.getBillsByCustomer(widget.customer.id);

        // Create PDF data for customer detail with transactions
        final pdfData = {
          'customerDetail': true,
          'customer': widget.customer,
          'bills': bills,
          'summary': {
            'totalBilled': _totalBilledAmount,
            'totalPaid': _totalPaidAmount,
            'netBalance': _netBalance,
          },
        };

        // Check if widget is still mounted before proceeding
        if (!mounted) return;

        // Store a local copy of the PDF data and settings
        final localPdfData = pdfData;
        final pdfSettings = PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': '${widget.customer.name} - Transaction Statement',
          },
        );

        // Capture the context before the async gap
        final capturedContext = context;

        // Use the universal PDF service to generate the PDF with modern design
        if (mounted) {
          // Create a local function to handle PDF generation
          Future<void> generatePdf() async {
            await UniversalPdfService.handlePdf(
              capturedContext,
              localPdfData,
              autoOpen: true,
              showSaveOption: true,
              showShareOption: true,
              pdfSettings: pdfSettings,
            );
          }

          // Call the local function
          await generatePdf();
        }
      } else {
        // Payments tab
        // Get payments for this customer
        final payments = await DatabaseService.getPaymentsByCustomer(widget.customer.id);

        // Create PDF data for payments
        final pdfData = {
          'payments': payments,
          'customer': widget.customer,
          'summary': {
            'totalAmount': payments.fold(0.0, (sum, payment) => sum + payment.amount),
            'count': payments.length,
          },
          'useModernTable': true, // Add this flag to use modern table
        };

        // Check if widget is still mounted before proceeding
        if (!mounted) return;

        // Store a local copy of the PDF data and settings
        final localPdfData = pdfData;
        final pdfSettings = PdfSettings.modernInvoice().copyWith(
          primaryColor: PdfColor.fromHex('#2E7D32'), // Green
          accentColor: PdfColor.fromHex('#1565C0'), // Blue
          additionalSettings: {
            'footerText': '${widget.customer.name} - Payment Report',
            'title': 'Payment Report',
          },
        );

        // Capture the context before the async gap
        final capturedContext = context;

        // Use the universal PDF service to generate the PDF
        if (mounted) {
          // Create a local function to handle PDF generation
          Future<void> generatePdf() async {
            await UniversalPdfService.handlePdf(
              capturedContext,
              localPdfData,
              autoOpen: true,
              showSaveOption: true,
              showShareOption: true,
              pdfSettings: pdfSettings,
            );
          }

          // Call the local function
          await generatePdf();
        }
      }
    } catch (e) {
      if (!mounted) return;

      // Show error message - we've already checked mounted above
      if (mounted) {
        // Create the error message
        final errorMessage = 'Error generating PDF: $e';

        // Capture the context before using it
        final capturedContext = context;

        // Show the error message
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(capturedContext).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCustomerSummary() async {
    try {
      setState(() {
        _isLoadingSummary = true;
      });

      // Use the optimized method to get all summary data at once
      final summaryData =
          await DatabaseService.getCustomerFinancialSummary(widget.customer.id);

      setState(() {
        _totalBilledAmount = summaryData['totalBilled'] as double;
        _totalPaidAmount = summaryData['totalPaid'] as double;
        _netBalance = summaryData['netBalance'] as double;
        _isLoadingSummary = false;
      });
    } catch (e) {
      // Error handled by setting isLoadingSummary to false
      setState(() {
        _isLoadingSummary = false;
      });
    }
  }

  // Make the loadCustomerSummary method public so tabs can call it
  Future<void> loadCustomerSummary() async {
    return _loadCustomerSummary();
  }

  // Build custom tab with individual colors
  Widget _buildCustomTab(String text, IconData icon, int tabIndex, Color inactiveColor) {
    final bool isSelected = _tabController.index == tabIndex;
    final Color textColor = isSelected ? Theme.of(context).primaryColor : inactiveColor;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: textColor,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TubewellAppBar(
        title: widget.customer.name,
        showBackButton: true,
        onPdfPressed: () => _handlePdfGeneration(context),
      ),
      body: Column(
        children: [
          // Tab bar with fixed height to prevent layout shifts
          PreferredSize(
            preferredSize: const Size.fromHeight(60.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(bottom: BorderSide(color: Colors.blue.shade200, width: 1)),
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Colors.transparent, // Hide default color
                indicatorColor: Theme.of(context).primaryColor,
                indicatorWeight: 3.0,
                tabs: [
                  Tab(
                    child: _buildCustomTab(
                      'SUMMARY',
                      Icons.analytics,
                      0,
                      Colors.teal.shade600,
                    ),
                  ),
                  Tab(
                    child: _buildCustomTab(
                      'BILLS',
                      Icons.receipt_long,
                      1,
                      Colors.purple.shade600,
                    ),
                  ),
                  Tab(
                    child: _buildCustomTab(
                      'PAYMENTS',
                      Icons.payments,
                      2,
                      Colors.indigo.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Tab content with summary card included in each tab
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CustomerSummaryTab(
                  key: _summaryTabKey,
                  customer: widget.customer,
                  onDataChanged: loadCustomerSummary,
                  totalBilledAmount: _totalBilledAmount,
                  totalPaidAmount: _totalPaidAmount,
                  netBalance: _netBalance,
                  isLoadingSummary: _isLoadingSummary,
                  onReminderPressed: _showReminderDialog,
                  onRefreshPressed: _loadCustomerSummary,
                ),
                CustomerTransactionsTab(
                  key: _transactionsTabKey,
                  customer: widget.customer,
                  onDataChanged: loadCustomerSummary,
                ),
                CustomerPaymentsTab(
                  key: _paymentsTabKey,
                  customer: widget.customer,
                  onDataChanged: loadCustomerSummary,
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _tabController.index == 0 ? null : FloatingActionButton(
        heroTag: 'fab-customer-detail-${_tabController.index}',
        backgroundColor: const Color(0xFF2E7D32),
        onPressed: () {
          if (_tabController.index == 1) {
            // Add transaction with smooth transition
            NavigationHelper.navigateWithSlide(
              context,
              TransactionFormScreen(
                selectedCustomer: widget.customer,
              ),
            ).then((result) {
              // Check if widget is still mounted
              if (!mounted) return;

              if (result == true) {
                // Force refresh on all tabs
                _refreshAllTabs();

                // Show success message - safe to use context as we've checked mounted
                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Transaction added successfully'),
                    backgroundColor: Color(0xFF2E7D32),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            });
          } else {
            // For the payments tab, navigate directly to the auto payment form
            NavigationHelper.navigateWithSlide(
              context,
              AutoPaymentFormScreen(
                customer: widget.customer,
              ),
            ).then((result) {
              // Check if widget is still mounted
              if (!mounted) return;

              if (result == true) {
                // Force refresh on all tabs
                _refreshAllTabs();

                // Show success message - safe to use context as we've checked mounted
                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Payment added successfully'),
                    backgroundColor: Color(0xFF2E7D32),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            });
          }
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }





  // Method to refresh all data in the details screen
  void _refreshAllTabs() {
    // Refresh the summary data
    _loadCustomerSummary();

    // Directly access and refresh the tab states using the global keys
    _transactionsTabKey.currentState?.refreshData();
    _paymentsTabKey.currentState?.refreshData();
    _summaryTabKey.currentState?.refreshData();

    // Force UI update
    setState(() {});
  }



  // Show reminder dialog for all unpaid bills
  void _showReminderDialog() async {
    try {
      // Show loading indicator
      setState(() {
        _isLoadingSummary = true;
      });

      // Get all unpaid bills for this customer
      final bills =
          await DatabaseService.getBillsByCustomer(widget.customer.id);
      final unpaidBills = bills.where((bill) => !bill.isPaid).toList();

      setState(() {
        _isLoadingSummary = false;
      });

      if (unpaidBills.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No unpaid bills to send reminders for'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => ReminderDialog(
            customer: widget.customer,
            bills: unpaidBills,
            title: 'Send Payment Reminder',
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoadingSummary = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading unpaid bills: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}