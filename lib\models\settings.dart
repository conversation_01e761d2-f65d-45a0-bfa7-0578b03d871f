/// Settings model for SQLite database
class Settings {
  int id;

  // Account ID this setting belongs to
  String? accountId;

  // Setting key
  String key;

  // Setting value
  String value;

  // Constructor
  Settings({
    this.id = 0,
    required this.key,
    required this.value,
    this.accountId,
  });

  // Convert a Settings object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'accountId': accountId,
      'key': key,
      'value': value,
    };
  }

  // Create a Settings object from a Map
  factory Settings.fromMap(Map<String, dynamic> map) {
    return Settings(
      id: map['id'],
      accountId: map['accountId'],
      key: map['key'],
      value: map['value'],
    );
  }
}
