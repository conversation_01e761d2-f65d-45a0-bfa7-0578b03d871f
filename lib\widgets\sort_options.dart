import 'package:flutter/material.dart';

enum SortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  customerNameAZ(label: 'Customer Name (A-Z)', icon: Icons.arrow_downward),
  customerNameZA(label: 'Customer Name (Z-A)', icon: Icons.arrow_upward),
  statusUnpaidFirst(label: 'Status (Unpaid first)', icon: Icons.priority_high),
  statusPaidFirst(
      label: 'Status (Paid first)', icon: Icons.check_circle_outline);

  final String label;
  final IconData icon;

  const SortOption({
    required this.label,
    required this.icon,
  });
}

/// A widget that displays a dropdown button for selecting sorting options
class SortOptionsDropdown extends StatelessWidget {
  final SortOption selectedOption;
  final ValueChanged<SortOption> onChanged;

  const SortOptionsDropdown({
    super.key,
    required this.selectedOption,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: PopupMenuButton<SortOption>(
        tooltip: 'Sort by',
        initialValue: selectedOption,
        onSelected: onChanged,
        itemBuilder: (context) => SortOption.values.map((option) {
          return PopupMenuItem<SortOption>(
            value: option,
            child: Row(
              children: [
                Icon(
                  option.icon,
                  color: option == selectedOption
                      ? Colors.teal
                      : Colors.grey.shade700,
                  size: 18,
                ),
                const SizedBox(width: 12),
                Text(
                  option.label,
                  style: TextStyle(
                    color:
                        option == selectedOption ? Colors.teal : Colors.black,
                    fontWeight: option == selectedOption
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
                const Spacer(),
                if (option == selectedOption)
                  const Icon(Icons.check, color: Colors.teal, size: 18)
              ],
            ),
          );
        }).toList(),
        icon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort,
              color: Colors.grey.shade700,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              'Sort',
              style: TextStyle(color: Colors.grey.shade700),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              color: Colors.grey.shade700,
            ),
          ],
        ),
      ),
    );
  }
}

/// A simple button that when pressed shows a modal with sorting options
class SortButton extends StatelessWidget {
  final SortOption selectedOption;
  final ValueChanged<SortOption> onOptionSelected;

  const SortButton({
    super.key,
    required this.selectedOption,
    required this.onOptionSelected,
  });

  void _showSortOptionsModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                child: Row(
                  children: [
                    const Icon(Icons.sort, color: Colors.teal),
                    const SizedBox(width: 8),
                    const Text(
                      'Sort by',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: ListView(
                  children: SortOption.values.map((option) {
                    final isSelected = option == selectedOption;
                    return ListTile(
                      leading: Icon(
                        option.icon,
                        color: isSelected ? Colors.teal : null,
                      ),
                      title: Text(option.label),
                      trailing: isSelected
                          ? const Icon(Icons.check_circle, color: Colors.teal)
                          : null,
                      selected: isSelected,
                      selectedColor: Colors.teal,
                      onTap: () {
                        onOptionSelected(option);
                        Navigator.pop(context);
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: IconButton(
        icon: Stack(
          alignment: Alignment.center,
          children: [
            const Icon(Icons.sort),
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.teal,
                ),
              ),
            ),
          ],
        ),
        tooltip: 'Sort: ${selectedOption.label}',
        onPressed: () => _showSortOptionsModal(context),
      ),
    );
  }
}
