import 'dart:async';
import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/customer_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart';
import 'package:tubewell_water_billing/forms/customer_form_screen.dart';
import 'package:tubewell_water_billing/screens/customer_detail_screen.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/universal_fab.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';

class CustomerListScreen extends StatefulWidget {
  const CustomerListScreen({
    super.key,
  });

  @override
  State<CustomerListScreen> createState() => _CustomerListScreenState();
}

class _CustomerListScreenState extends State<CustomerListScreen>
    with AutomaticKeepAliveClientMixin {
  // Keep state alive only when we have customer data
  @override
  bool get wantKeepAlive => _allCustomers.isNotEmpty;

  // Use a future instead of loading in initState
  late Future<List<Customer>> _customersFuture;

  // Search controller
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Sort variables
  bool _isSortActive = false;
  String _sortOption = 'nameAsc'; // Default sort by name ascending
  final Map<String, String> _sortLabels = {
    'nameAsc': 'Name (A-Z)',
    'nameDesc': 'Name (Z-A)',
    'creditDesc': 'Highest Credit First',
    'dueDesc': 'Highest Due First'
  };

  // Pagination variables
  static const int _pageSize = 20;
  int _currentPage = 0;
  List<Customer> _allCustomers = [];
  List<Customer> _displayedCustomers = [];
  bool _isLoadingMore = false;
  final ScrollController _scrollController = ScrollController();

  // Data change subscription
  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  @override
  void initState() {
    super.initState();
    _customersFuture = _loadCustomers();
    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_scrollListener);

    // Listen for data changes that affect customers
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      // Refresh customer data when relevant data changes
      if (changeType == DataChangeType.customer ||
          changeType == DataChangeType.payment ||
          changeType == DataChangeType.bill ||
          changeType == DataChangeType.all) {
        if (mounted) {
          debugPrint(
              'CustomerListScreen: Refreshing due to ${changeType.toString()} change');
          _refreshCustomers();
        }
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _dataChangeSubscription.cancel();
    _debounceTimer?.cancel(); // Cancel the debounce timer
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 500 &&
        !_isLoadingMore &&
        _displayedCustomers.length < _allCustomers.length) {
      _loadMoreItems();
    }
  }

  void _loadMoreItems() {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    _currentPage++;
    _loadCustomers().then((_) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }).catchError((e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading more customers: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  // Debounce search to reduce unnecessary rebuilds
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      if (mounted) {
        setState(() {
          _searchQuery = _searchController.text.toLowerCase();
          _applySearch();
        });
      }
    });
  }

  void _applySearch() {
    // Reload customers with current search and sort options
    _refreshCustomers();
  }

  // New method to apply sorting to the customer list
  Future<void> _applySorting(List<Customer> customers) async {
    switch (_sortOption) {
      case 'nameAsc':
        customers.sort((a, b) => a.name.compareTo(b.name));
        break;

      case 'nameDesc':
        customers.sort((a, b) => b.name.compareTo(a.name));
        break;

      case 'creditDesc':
        // Sort by credit balance (highest first)
        await _sortByBalance(customers, true);
        break;

      case 'dueDesc':
        // Sort by due amount (highest first)
        await _sortByBalance(customers, false);
        break;

      default:
        // Default is name ascending
        customers.sort((a, b) => a.name.compareTo(b.name));
    }
  }

  Future<void> _sortByBalance(List<Customer> customers, bool sortByCredit) async {
    // Get balance data for all customers
    final Map<int, Map<String, dynamic>> balanceMap = {};

    for (final customer in customers) {
      try {
        final balanceDetails = await CustomerService.getCustomerBalanceDetails(customer.id);
        balanceMap[customer.id] = balanceDetails;
      } catch (e) {
        // If balance fetch fails, use default values
        balanceMap[customer.id] = {
          'outstandingAmount': 0.0,
          'creditAmount': 0.0,
          'netBalance': 0.0,
          'hasOutstanding': false,
          'hasCredit': false,
        };
      }
    }

    // Sort based on the selected criteria
    customers.sort((a, b) {
      final balanceA = balanceMap[a.id] ?? {'netBalance': 0.0};
      final balanceB = balanceMap[b.id] ?? {'netBalance': 0.0};

      if (sortByCredit) {
        // Sort by credit balance (positive netBalance means customer has credit)
        final creditA = balanceA['netBalance'] > 0 ? balanceA['netBalance'] : 0.0;
        final creditB = balanceB['netBalance'] > 0 ? balanceB['netBalance'] : 0.0;
        return creditB.compareTo(creditA); // Descending order
      } else {
        // Sort by due amount (negative netBalance means customer owes money)
        final dueA = balanceA['netBalance'] < 0 ? balanceA['netBalance'].abs() : 0.0;
        final dueB = balanceB['netBalance'] < 0 ? balanceB['netBalance'].abs() : 0.0;
        return dueB.compareTo(dueA); // Descending order
      }
    });
  }

  Future<List<Customer>> _loadCustomers() async {
    try {
      debugPrint("CUSTOMERS_SCREEN: Attempting to load customers.");
      final activeAccountIdFromAccountService = AccountService.currentAccount?.id;
      debugPrint("CUSTOMERS_SCREEN: AccountService.currentAccount.id = $activeAccountIdFromAccountService");

      final accountIdForDbQuery = DatabaseService.getCurrentAccountId();
      debugPrint("CUSTOMERS_SCREEN: DatabaseService.getCurrentAccountId() for query = $accountIdForDbQuery");

      if (activeAccountIdFromAccountService == null || accountIdForDbQuery == null) {
        debugPrint("CUSTOMERS_SCREEN: ABORTING FETCH - account ID is null.");
        if (mounted) {
          setState(() {
            _allCustomers = [];
            _displayedCustomers = [];
          });
        }
        return [];
      }

      if (activeAccountIdFromAccountService != accountIdForDbQuery) {
        debugPrint("CUSTOMERS_SCREEN: MISMATCH! AccountService ID ($activeAccountIdFromAccountService) vs DBService ID ($accountIdForDbQuery)");
      }

      // Load all customers from database (we'll handle pagination in memory for now)
      final allCustomers = await DatabaseService.getAllCustomers();
      debugPrint("CUSTOMERS_SCREEN: Fetched ${allCustomers.length} customers.");
      if (allCustomers.isNotEmpty) {
        debugPrint("CUSTOMERS_SCREEN: First customer name: ${allCustomers.first.name}");
      }

      // Filter by search query if needed
      List<Customer> filteredCustomers = allCustomers;
      if (_searchQuery.isNotEmpty) {
        filteredCustomers = allCustomers.where((customer) {
          final customerName = customer.name.toLowerCase();
          final contactNumber = customer.contactNumber?.toLowerCase() ?? '';
          return customerName.contains(_searchQuery) ||
              contactNumber.contains(_searchQuery);
        }).toList();
      }

      // Apply sorting
      await _applySorting(filteredCustomers);

      // Apply pagination
      final startIndex = _currentPage * _pageSize;
      final endIndex = (startIndex + _pageSize).clamp(0, filteredCustomers.length);
      final paginatedCustomers = filteredCustomers.sublist(
        startIndex.clamp(0, filteredCustomers.length),
        endIndex,
      );

      // Store all customers and displayed customers
      if (mounted) {
        setState(() {
          _allCustomers = filteredCustomers; // Store filtered results
          _displayedCustomers = paginatedCustomers;
        });
      }

      return paginatedCustomers;
    } catch (e) {
      throw Exception('Failed to load customers: $e');
    }
  }

  Future<void> _refreshCustomers() async {
    if (mounted) {
      setState(() {
        _customersFuture = _loadCustomers();
      });
      // Wait for the future to complete
      await _customersFuture;
    }
  }

  void _deleteCustomer(Customer customer) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Expanded(
                child: Text('Deleting customer...'),
              ),
            ],
          ),
        );
      },
    );

    try {
      // Debug: Attempting to delete customer: ${customer.name} (ID: ${customer.id})
      final result =
          await CustomerService.deleteCustomerWithAllData(customer.id);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Hide loading indicator
      Navigator.of(context).pop();

      if (result['success']) {
        // Debug: Customer deletion successful: ${customer.name}
        // Refresh the customer list
        _refreshCustomers();

        // Show success message with deletion details
        _showDeletionSummary(context, result);
      } else {
        // Debug: Customer deletion failed: ${result['error']}
        // Show detailed error message
        _showErrorDialog(context, 'Failed to Delete Customer',
            'Could not delete ${customer.name}.\n\nReason: ${result['error']}');
      }
    } catch (e) {
      // Debug: Exception during customer deletion: $e

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Hide loading indicator
      Navigator.of(context).pop();

      // Show detailed error message
      _showErrorDialog(context, 'Error Deleting Customer',
          'An unexpected error occurred while trying to delete ${customer.name}.\n\nError details: $e');
    }
  }

  void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(title, style: const TextStyle(color: Colors.red)),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange.shade700),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Tip: Make sure all related data (bills, payments, etc.) is properly synced before deleting a customer.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDeletionSummary(BuildContext context, Map<String, dynamic> result) {
    final deletedItems = result['deletedItems'] as Map<String, dynamic>;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.green.shade700),
            const SizedBox(width: 8),
            const Expanded(child: Text('Customer Deleted')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Successfully deleted ${result['customerName']}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text('The following data was also removed:'),
            const SizedBox(height: 8),

            // Show deleted bills
            _buildDeletionSummaryItem(
              Icons.receipt,
              '${deletedItems['bills']} ${deletedItems['bills'] == 1 ? 'bill' : 'bills'}',
            ),

            // Show deleted payments
            _buildDeletionSummaryItem(
              Icons.payment,
              '${deletedItems['payments']} ${deletedItems['payments'] == 1 ? 'payment' : 'payments'}',
            ),

            // Show deleted credit
            _buildDeletionSummaryItem(
              Icons.account_balance_wallet,
              'Credit: ${deletedItems['credit']}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildDeletionSummaryItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade700),
          const SizedBox(width: 8),
          Text(text),
        ],
      ),
    );
  }

  // Generate PDF for customer list
  Future<void> _generateCustomerListPdf() async {
    if (!mounted) return;

    try {
      // Get all customers
      final allCustomers = await DatabaseService.getAllCustomers();

      // Get customer summary
      final summary = await CustomerService.getCustomersSummary();

      if (!mounted) return;

      // Create PDF data
      final Map<String, dynamic> pdfData = {
        'customers': allCustomers,
        'summary': summary,
        'filters': {
          'searchQuery': _searchQuery.isNotEmpty ? _searchQuery : null,
          'sortOption': _sortLabels[_sortOption] ?? 'Name (A-Z)',
        },
      };

      // Use the universal PDF service to generate the PDF with modern design
      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': 'Customer List Report',
            'title': 'Customer Directory',
          },
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Scaffold(
      appBar: TubewellAppBar(
        title: 'All Customers',
        currentScreen: 'customers',
        showBackButton: false,
        onPdfPressed: _generateCustomerListPdf,
      ),
      drawer: const AppDrawer(currentScreen: 'customers'),
      body: _buildContent(),
      floatingActionButton: UniversalFab(
        type: FabType.customer,
        heroTag: 'fab-customer-list',
        onResult: (result) {
          if (result) {
            _refreshCustomers();
          }
        },
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // Search bar - keep fixed at top
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search customers...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                    contentPadding: const EdgeInsets.symmetric(vertical: 10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                      borderSide:
                          BorderSide(color: Colors.blue.shade400, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: _isSortActive ? const Color(0xFF2E7D32) : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.sort,
                    color: _isSortActive ? Colors.white : Colors.grey.shade700,
                  ),
                  onPressed: _showSortDialog,
                  tooltip: 'Sort',
                ),
              ),
            ],
          ),
        ),

        // Filter indicators (similar to other screens)
        if (_searchQuery.isNotEmpty || _isSortActive)
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
            child: Row(
              children: [
                Text(
                  'Found ${_displayedCustomers.length} results',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const Spacer(),
                if (_isSortActive)
                  InkWell(
                    onTap: () {
                      setState(() {
                        _sortOption = 'nameAsc';
                        _isSortActive = false;
                        _applySearch();
                      });
                    },
                    borderRadius: BorderRadius.circular(16),
                    child: Chip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.sort, size: 16),
                          const SizedBox(width: 4),
                          Text(_sortLabels[_sortOption] ?? 'Sorted'),
                          const SizedBox(width: 4),
                          const Icon(Icons.close, size: 16),
                        ],
                      ),
                      backgroundColor: const Color(0xFFE8F5E9),
                      labelStyle: const TextStyle(color: Color(0xFF2E7D32)),
                    ),
                  ),
                if (_searchQuery.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      _searchController.clear();
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: const Color(0xFF2E7D32),
                    ),
                    child: const Text('Clear Search'),
                  ),
              ],
            ),
          ),

        // Scrollable content area
        Expanded(
          child: FutureBuilder<List<Customer>>(
            future: _customersFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(child: Text('Error: ${snapshot.error}'));
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return _buildScrollableContent(isEmpty: true);
              } else {
                return _buildScrollableContent(isEmpty: false);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildScrollableContent({required bool isEmpty}) {
    if (isEmpty) {
      return FutureBuilder<Map<String, dynamic>>(
        future: _getCustomersSummary(),
        builder: (context, summarySnapshot) {
          return RefreshIndicator(
            onRefresh: _refreshCustomers,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  // Summary card
                  if (summarySnapshot.connectionState == ConnectionState.waiting)
                    const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
                    )
                  else
                    _buildCustomerSummaryCard(summarySnapshot.data ?? {
                      'totalCount': 0,
                      'totalCredit': 0.0,
                      'totalDue': 0.0,
                      'activeCount': 0,
                    }),

                  // Empty state
                  EmptyStateWidget(
                    icon: Icons.people,
                    title: 'No Customers Yet',
                    message: 'Add your first customer to get started with billing.',
                    buttonText: 'Add Customer',
                    onButtonPressed: () {
                      _showCustomerAddOptions(context);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      );
    }

    return FutureBuilder<Map<String, dynamic>>(
      future: _getCustomersSummary(),
      builder: (context, summarySnapshot) {
        return RefreshIndicator(
          onRefresh: _refreshCustomers,
          child: CustomScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // Summary card as a sliver
              SliverToBoxAdapter(
                child: summarySnapshot.connectionState == ConnectionState.waiting
                    ? const Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
                      )
                    : _buildCustomerSummaryCard(summarySnapshot.data ?? {
                        'totalCount': 0,
                        'totalCredit': 0.0,
                        'totalDue': 0.0,
                        'activeCount': 0,
                      }),
              ),

              // Customer list as a sliver
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (index >= _displayedCustomers.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final customer = _displayedCustomers[index];
                    return _CustomerListItem(
                      customer: customer,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CustomerDetailScreen(
                              customer: customer,
                            ),
                          ),
                        ).then((_) {
                          // Refresh data when returning from customer detail screen
                          if (mounted) {
                            _refreshCustomers();
                          }
                        });
                      },
                      onDelete: () {
                        _deleteCustomer(customer);
                      },
                      onEdit: () async {
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CustomerFormScreen(
                              customer: customer,
                            ),
                          ),
                        );
                        if (result == true && mounted) {
                          _refreshCustomers();
                        }
                      },
                    );
                  },
                  childCount: _displayedCustomers.length + (_isLoadingMore ? 1 : 0),
                ),
              ),
            ],
          ),
        );
      },
    );
  }



  // Add this new method to fetch customer summary data
  Future<Map<String, dynamic>> _getCustomersSummary() async {
    try {
      final summary = await CustomerService.getCustomersSummary();
      return summary;
    } catch (e) {
      return {
        'totalCount': 0,
        'activeCount': 0,
        'totalCredit': 0.0,
        'totalDue': 0.0,
      };
    }
  }

  // Enhanced customer summary card that matches the style from payment and expense screens
  Widget _buildCustomerSummaryCard(Map<String, dynamic> summary) {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Customers Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade900,
                  fontSize: 18,
                ),
              ),
              Text(
                '${summary['totalCount']} customers',
                style: TextStyle(
                  color: Colors.blue.shade800,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight - Active Customers
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.people,
                      color: Colors.blue.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Active Customers',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${summary['activeCount'] ?? 0}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Detailed stats
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Total Credit',
                  value: CurrencyService.formatCurrency(
                      summary['totalCredit'] ?? 0.0),
                  icon: Icons.account_balance_wallet,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Total Due',
                  value: CurrencyService.formatCurrency(
                      summary['totalDue'] ?? 0.0),
                  icon: Icons.money_off,
                  iconColor: Colors.red.shade700,
                  bgColor: Colors.red.shade50,
                  borderColor: Colors.red.shade200,
                  textColor: Colors.red.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color textColor,
    required Color borderColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 20),
              const SizedBox(width: 6),
              Text(
                title,
                style: TextStyle(
                  color: textColor,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: textColor,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Replace filter dialog with sort dialog
  void _showCustomerAddOptions(BuildContext context) {
    // Since we removed the contact import feature, directly navigate to add customer manually
    _handleAddCustomerManually(context);
  }

  Future<void> _handleAddCustomerManually(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomerFormScreen(),
      ),
    );
    if (result == true && mounted) {
      _refreshCustomers();
    }
  }

  void _showSortDialog() {
    // Temporary variable to store sort option during dialog
    String tempSortOption = _sortOption;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(Icons.sort, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text('Sort Customers'),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RadioListTile<String>(
                    title: Row(
                      children: [
                        Icon(Icons.arrow_upward,
                            size: 20, color: Theme.of(context).primaryColor),
                        const SizedBox(width: 8),
                        const Text('Name (A-Z)'),
                      ],
                    ),
                    value: 'nameAsc',
                    groupValue: tempSortOption,
                    onChanged: (value) {
                      if (value != null) {
                        setDialogState(() {
                          tempSortOption = value;
                        });
                      }
                    },
                    dense: true,
                    activeColor: Theme.of(context).primaryColor,
                  ),
                  RadioListTile<String>(
                    title: Row(
                      children: [
                        Icon(Icons.arrow_downward,
                            size: 20, color: Theme.of(context).primaryColor),
                        const SizedBox(width: 8),
                        const Text('Name (Z-A)'),
                      ],
                    ),
                    value: 'nameDesc',
                    groupValue: tempSortOption,
                    onChanged: (value) {
                      if (value != null) {
                        setDialogState(() {
                          tempSortOption = value;
                        });
                      }
                    },
                    dense: true,
                    activeColor: Theme.of(context).primaryColor,
                  ),
                  const Divider(height: 16),
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.0),
                    child: Text('Balance-Based',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                  RadioListTile<String>(
                    title: Row(
                      children: [
                        Icon(Icons.account_balance_wallet,
                            size: 20, color: Theme.of(context).primaryColor),
                        const SizedBox(width: 8),
                        const Text('Highest Credit First'),
                      ],
                    ),
                    value: 'creditDesc',
                    groupValue: tempSortOption,
                    onChanged: (value) {
                      if (value != null) {
                        setDialogState(() {
                          tempSortOption = value;
                        });
                      }
                    },
                    dense: true,
                    activeColor: Theme.of(context).primaryColor,
                  ),
                  RadioListTile<String>(
                    title: Row(
                      children: [
                        Icon(Icons.money_off,
                            size: 20, color: Theme.of(context).primaryColor),
                        const SizedBox(width: 8),
                        const Text('Highest Due First'),
                      ],
                    ),
                    value: 'dueDesc',
                    groupValue: tempSortOption,
                    onChanged: (value) {
                      if (value != null) {
                        setDialogState(() {
                          tempSortOption = value;
                        });
                      }
                    },
                    dense: true,
                    activeColor: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Wrap(
                  alignment: WrapAlignment.spaceEvenly,
                  spacing: 8.0,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // Clear sorting - reset to default
                        setState(() {
                          _sortOption = 'nameAsc';
                          _isSortActive = false;
                          _applySearch();
                        });
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Reset'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // Apply selected sort option
                        setState(() {
                          _sortOption = tempSortOption;
                          _isSortActive = _sortOption !=
                              'nameAsc'; // Only mark as active if not default
                          _applySearch();
                        });
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7D32),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                      ),
                      child: const Text('Apply'),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

// Extract customer item to a separate widget to reduce rebuilds
class _CustomerListItem extends StatefulWidget {
  final Customer customer;
  final VoidCallback onTap;
  final VoidCallback onDelete;
  final VoidCallback onEdit;

  const _CustomerListItem({
    required this.customer,
    required this.onTap,
    required this.onDelete,
    required this.onEdit,
  });

  @override
  State<_CustomerListItem> createState() => _CustomerListItemState();
}

class _CustomerListItemState extends State<_CustomerListItem> {
  double? _balance;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBalance();
  }

  Future<void> _loadBalance() async {
    try {
      final balance =
          await CustomerService.getCustomerBalance(widget.customer.id);
      if (mounted) {
        setState(() {
          _balance = balance;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _balance = null;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        contentPadding: const EdgeInsets.only(left: 16.0, right: 4.0),
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF2E7D32),
          child: Text(
            widget.customer.name.isNotEmpty
                ? widget.customer.name[0].toUpperCase()
                : '?',
            style: const TextStyle(color: Colors.white),
          ),
        ),
        title: Row(
          children: [
            Expanded(
              flex: 3,
              child: Text(
                widget.customer.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            if (_isLoading)
              const SizedBox(
                height: 12,
                width: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              )
            else if (_balance != null)
              _buildBalanceText(_balance!),
          ],
        ),
        subtitle: widget.customer.contactNumber != null &&
                widget.customer.contactNumber!.isNotEmpty
            ? Text(widget.customer.contactNumber!)
            : null,
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Color(0xFF2E7D32)),
          padding: EdgeInsets.zero,
          onSelected: (value) {
            if (value == 'edit') {
              widget.onEdit();
            } else if (value == 'delete') {
              _showDeleteConfirmation(context);
            }
          },
          itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
            const PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Color(0xFF2E7D32)),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: widget.onTap,
      ),
    );
  }

  Widget _buildBalanceText(double balance) {
    final formattedBalance = CurrencyService.formatCurrency(balance.abs());

    if (balance < 0) {
      // Outstanding balance (negative)
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.red.shade200),
        ),
        child: Text(
          'Due: $formattedBalance',
          style: const TextStyle(
            color: Colors.red,
            fontWeight: FontWeight.bold,
            fontSize: 11,
          ),
        ),
      );
    } else if (balance > 0) {
      // Credit balance (positive)
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
        decoration: BoxDecoration(
          color: Colors.purple.shade50,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.purple.shade200),
        ),
        child: Text(
          'Credit: $formattedBalance',
          style: const TextStyle(
            color: Colors.purple,
            fontWeight: FontWeight.bold,
            fontSize: 11,
          ),
        ),
      );
    } else {
      // Zero balance
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: Text(
          'Bal: 0',
          style: TextStyle(
            color: Colors.blue.shade700,
            fontWeight: FontWeight.bold,
            fontSize: 11,
          ),
        ),
      );
    }
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Expanded(
              child:
                  Text('Delete Customer', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Are you sure you want to delete ${widget.customer.name}?',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'This will permanently delete:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    _buildDeleteWarningItem(
                        Icons.person, 'Customer information'),
                    _buildDeleteWarningItem(
                        Icons.receipt, 'All bills for this customer'),
                    _buildDeleteWarningItem(
                        Icons.payment, 'All payment records'),
                    _buildDeleteWarningItem(
                        Icons.account_balance_wallet, 'Any credit balance'),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This action cannot be undone.',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onDelete();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  Widget _buildDeleteWarningItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.red.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(color: Colors.red.shade800),
            ),
          ),
        ],
      ),
    );
  }
}
