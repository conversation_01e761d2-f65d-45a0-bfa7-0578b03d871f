import 'dart:async';

/// Types of data changes that can occur in the app
enum DataChangeType {
  bill, // Bill created, updated or deleted
  payment, // Payment created, updated or deleted
  customer, // Customer data changed
  expense, // Expense data changed
  settings, // App settings changed
  all // Force refresh everything
}

/// A service that notifies listeners about data changes
class DataChangeNotifierService {
  // Singleton instance
  static final DataChangeNotifierService _instance =
      DataChangeNotifierService._internal();

  // Private constructor
  DataChangeNotifierService._internal();

  // Factory constructor to access singleton instance
  factory DataChangeNotifierService() => _instance;

  // StreamController for data change events
  final _dataChangeController = StreamController<DataChangeType>.broadcast();

  // Stream getter that listeners can subscribe to
  Stream<DataChangeType> get onDataChanged => _dataChangeController.stream;

  // Method to notify listeners that data has changed
  void notifyDataChanged(DataChangeType changeType) {
    _dataChangeController.add(changeType);

    // Log notification for debugging
    // Uncomment for debugging:
    // debugPrint('DataChangeNotifierService: Notified about ${changeType.toString()} change');
  }

  // Dispose method to clean up
  void dispose() {
    _dataChangeController.close();
  }
}
