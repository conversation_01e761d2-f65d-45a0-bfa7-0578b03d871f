import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/currency.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/payment_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/forms/auto_payment_form_screen.dart';
import 'package:tubewell_water_billing/widgets/info_chips.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/screens/bill_details_screen.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';

// Sort options for payments
enum PaymentSortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  methodAZ(label: 'Payment Method (A-Z)', icon: Icons.arrow_downward),
  methodZA(label: 'Payment Method (Z-A)', icon: Icons.arrow_upward);

  final String label;
  final IconData icon;

  const PaymentSortOption({
    required this.label,
    required this.icon,
  });
}

class PaymentsScreen extends StatefulWidget {
  final Customer? selectedCustomer;

  const PaymentsScreen({
    super.key,
    this.selectedCustomer,
  });

  // Static method to show customer selection dialog for payment
  static void showCustomerSelectionForPayment(BuildContext context,
      {VoidCallback? onPaymentAdded}) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Select Customer for Payment'),
        content: FutureBuilder<List<Customer>>(
          future: DatabaseService.getAllCustomers(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return const Text('Error loading customers');
            }

            final customers = snapshot.data ?? [];

            if (customers.isEmpty) {
              return const Text(
                  'No customers found. Please add a customer first.');
            }

            return SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: customers.length,
                itemBuilder: (context, index) {
                  final customer = customers[index];
                  return ListTile(
                    title: Text(customer.name),
                    subtitle: customer.contactNumber != null
                        ? Text(customer.contactNumber!)
                        : null,
                    onTap: () {
                      Navigator.of(dialogContext).pop();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AutoPaymentFormScreen(
                            customer: customer,
                          ),
                        ),
                      ).then((result) {
                        // If payment was added successfully, refresh
                        if (result == true && onPaymentAdded != null) {
                          onPaymentAdded();
                        }
                      });
                    },
                  );
                },
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }

  @override
  State<PaymentsScreen> createState() => _PaymentsScreenState();
}

class _PaymentsScreenState extends State<PaymentsScreen>
    with AutomaticKeepAliveClientMixin {
  // Keep state alive only when we have payment data
  @override
  bool get wantKeepAlive => _displayedPayments.isNotEmpty;

  // Stream controller for payments
  List<Payment> _displayedPayments = [];
  bool _isLoading = false;
  bool _hasMoreItems = true;
  Map<int, Customer> _customersMap = {};

  // Multi-selection state
  bool _isSelectionMode = false;
  final Set<int> _selectedPaymentIds = {};

  // Search controller
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Pagination variables
  static const int _pageSize = 20;
  int _currentOffset = 0;
  final ScrollController _scrollController = ScrollController();

  // Track customer credit
  double _customerCredit = 0;
  bool _isLoadingCredit = false;

  // Stream subscription for currency changes
  late final StreamSubscription<Currency> _currencySubscription;

  // Stream subscription for data changes
  late final StreamSubscription<DataChangeType> _dataChangeSubscription;

  // Sort state
  PaymentSortOption _currentSortOption = PaymentSortOption.dateNewest;
  bool _isSortActive = false;

  @override
  void initState() {
    super.initState();
    _loadCustomersMap();
    _loadPayments();
    _searchController.addListener(_onSearchChanged);
    _scrollController.addListener(_scrollListener);

    if (widget.selectedCustomer != null) {
      _loadCustomerCredit();
    }

    // Listen for currency changes
    _currencySubscription = CurrencyService.onCurrencyChanged.listen((_) {
      // When currency changes, refresh the UI
      if (mounted) {
        setState(() {
          // Just trigger a rebuild to update currency formatting
        });
      }
    });

    // Listen for data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      // Refresh when payments or bills are changed or when a force refresh is requested
      if (changeType == DataChangeType.payment ||
          changeType == DataChangeType.bill ||
          changeType == DataChangeType.all) {
        if (mounted) {
          debugPrint(
              'PaymentsScreen: Refreshing due to ${changeType.toString()} change');
          _refreshPayments();
        }
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _currencySubscription.cancel();
    _dataChangeSubscription.cancel();
    _debounceTimer?.cancel(); // Cancel the debounce timer
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 500 &&
        !_isLoading &&
        _hasMoreItems) {
      _loadMoreItems();
    }
  }

  Future<void> _loadCustomersMap() async {
    try {
      // Load all customers to display their names
      final customers = await DatabaseService.getAllCustomers();

      // Create a map of customer IDs to customer names for easy lookup
      setState(() {
        _customersMap = {for (var c in customers) c.id: c};
      });
    } catch (e) {
      // Debug: Error loading customers map: $e
    }
  }

  Future<void> _loadPayments() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Reset current offset and displayed payments
      _currentOffset = 0;

      List<Payment> payments;
      // Use the updated methods with pagination and search
      if (widget.selectedCustomer != null) {
        payments = await DatabaseService.getPaymentsByCustomer(
          widget.selectedCustomer!.id,
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      } else {
        payments = await DatabaseService.getAllPayments(
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      }

      // Apply sorting
      _sortPayments(payments);

      setState(() {
        _displayedPayments = payments;
        _currentOffset = payments.length;
        _hasMoreItems = payments.length >= _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Debug: Error loading payments: $e
    }
  }

  void _loadMoreItems() async {
    if (_isLoading || !_hasMoreItems) return;

    setState(() {
      _isLoading = true;
    });

    try {
      List<Payment> morePayments;
      if (widget.selectedCustomer != null) {
        morePayments = await DatabaseService.getPaymentsByCustomer(
          widget.selectedCustomer!.id,
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      } else {
        morePayments = await DatabaseService.getAllPayments(
          offset: _currentOffset,
          limit: _pageSize,
          searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        );
      }

      setState(() {
        _displayedPayments.addAll(morePayments);
        // Apply sorting to the complete list
        _sortPayments(_displayedPayments);
        _currentOffset += morePayments.length;
        _hasMoreItems = morePayments.length >= _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Debug: Error loading more payments: $e
    }
  }

  // Debounce search to reduce unnecessary rebuilds
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      if (mounted && _searchQuery != _searchController.text.toLowerCase()) {
        setState(() {
          _searchQuery = _searchController.text.toLowerCase();
        });
        // Reset and reload
        _loadPayments();
      }
    });
  }

  Future<void> _refreshPayments() async {
    // Reload payments and customer credit (if applicable)
    await _loadPayments();
    if (widget.selectedCustomer != null) {
      await _loadCustomerCredit();
    }
  }

  Future<void> _loadCustomerCredit() async {
    if (widget.selectedCustomer == null) return;

    setState(() {
      _isLoadingCredit = true;
    });

    try {
      // Debug: Loading credit for customer ${widget.selectedCustomer!.id}
      final creditAmount = await PaymentService.getCustomerCreditBalance(
          widget.selectedCustomer!.id);
      // Debug: Credit balance loaded: $creditAmount

      if (!mounted) return;
      setState(() {
        _customerCredit = creditAmount;
        _isLoadingCredit = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoadingCredit = false;
      });
      // Debug: Error loading customer credit: $e
    }
  }

  // Sort payments based on current sort option
  void _sortPayments(List<Payment> payments) {
    switch (_currentSortOption) {
      case PaymentSortOption.dateNewest:
        payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
        break;
      case PaymentSortOption.dateOldest:
        payments.sort((a, b) => a.paymentDate.compareTo(b.paymentDate));
        break;
      case PaymentSortOption.amountHighest:
        payments.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case PaymentSortOption.amountLowest:
        payments.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case PaymentSortOption.methodAZ:
        payments.sort((a, b) {
          final methodA = a.paymentMethod ?? '';
          final methodB = b.paymentMethod ?? '';
          final comparison = methodA.compareTo(methodB);
          return comparison != 0 ? comparison : b.paymentDate.compareTo(a.paymentDate);
        });
        break;
      case PaymentSortOption.methodZA:
        payments.sort((a, b) {
          final methodA = a.paymentMethod ?? '';
          final methodB = b.paymentMethod ?? '';
          final comparison = methodB.compareTo(methodA);
          return comparison != 0 ? comparison : b.paymentDate.compareTo(a.paymentDate);
        });
        break;
    }
  }

  // Handle sort option change
  void _onSortChanged(PaymentSortOption option) {
    setState(() {
      _currentSortOption = option;
      _isSortActive = option != PaymentSortOption.dateNewest;
    });

    // Re-sort current payments
    _sortPayments(_displayedPayments);
    setState(() {});
  }

  // Show sort options dialog
  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Payments'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: PaymentSortOption.values.map((option) {
            final isSelected = option == _currentSortOption;
            return ListTile(
              leading: Icon(
                option.icon,
                color: isSelected ? Colors.purple : null,
              ),
              title: Text(option.label),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.purple)
                  : null,
              selected: isSelected,
              selectedColor: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _onSortChanged(option);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  // Class level _deletePayment method with confirmation
  void _deletePayment(Payment payment) async {
    if (!mounted) return;

    try {
      // First check if this payment is associated with a bill
      if (payment.billId > 0) {
        // Show a more detailed confirmation dialog
        final shouldDelete = await _showDeletionImpactDialog(payment);
        if (!shouldDelete || !mounted) return;

        // Delete the payment and handle bill status updates
        await PaymentService.deletePaymentAndUpdateBillStatus(payment);
      } else {
        // For payments not linked to bills, show simple confirmation
        final shouldDelete = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Payment'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Are you sure you want to delete this payment of ${CurrencyService.formatCurrency(payment.amount)}?',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text('Payment ID: ${payment.id}'),
                  Text(
                      'Customer: ${_customersMap[payment.customerId]?.name ?? "Unknown"}'),
                  Text(
                      'Date: ${DateFormat('yyyy-MM-dd').format(payment.paymentDate)}'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
                child: const Text('DELETE'),
              ),
            ],
          ),
        );

        if (shouldDelete != true || !mounted) return;

        // Just delete the payment
        await DatabaseService.deletePayment(payment.id);

        // Notify other parts of the app that payment data has changed
        DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);

        // If payment was linked to a bill, notify about bill changes too
        if (payment.billId > 0) {
          DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
        }

        // Also notify about customer changes since balance is affected
        DataChangeNotifierService().notifyDataChanged(DataChangeType.customer);
      }

      if (!mounted) return;
      setState(() {
        _displayedPayments.remove(payment);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Payment deleted successfully')),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete payment: $e')),
      );
    }
  }

  // New method to show a more detailed confirmation dialog
  Future<bool> _showDeletionImpactDialog(Payment payment) async {
    final customer = _customersMap[payment.customerId];
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Payment',
            style: TextStyle(color: Colors.red.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Warning: This payment is linked to a bill. Deleting it will:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Impact details
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning,
                            color: Colors.red.shade700, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Update Bill #${payment.billId} status',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade900),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• If this was a full payment, the bill will be marked as unpaid again',
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                    Text(
                      '• If this was a partial payment, the partial amount will be removed',
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              Text(
                'Are you sure you want to delete this payment of ${CurrencyService.formatCurrency(payment.amount)}?',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text('Payment ID: ${payment.id}'),
              Text('Customer: ${customer?.name ?? "Unknown"}'),
              Text(
                  'Date: ${DateFormat('yyyy-MM-dd').format(payment.paymentDate)}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Scaffold(
      appBar: _isSelectionMode
          ? AppBar(
              backgroundColor: const Color(0xFF2E7D32),
              title: Text('${_selectedPaymentIds.length} selected'),
              leading: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = false;
                    _selectedPaymentIds.clear();
                  });
                },
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: _selectedPaymentIds.isEmpty
                      ? null
                      : () => _deleteSelectedPayments(),
                ),
              ],
            )
          : TubewellAppBar(
              title: widget.selectedCustomer != null
                  ? '${widget.selectedCustomer!.name} Payments'
                  : 'Payments',
              currentScreen: 'payments',
              showBackButton: false,
              pdfData: {
                'payments': _displayedPayments,
                'customer': widget.selectedCustomer,
                'showPayments': true,
              },
            ),
      drawer: const AppDrawer(currentScreen: 'payments'),
      body: _buildContent(),
      floatingActionButton: _isSelectionMode
          ? null
          : FloatingActionButton(
              heroTag: 'fab-payments',
              backgroundColor: const Color(0xFF2E7D32),
              onPressed: () {
                PaymentsScreen.showCustomerSelectionForPayment(
                  context,
                  onPaymentAdded: _refreshPayments,
                );
              },
              child: const Icon(Icons.add, color: Colors.white),
            ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // Search bar - keep fixed at top
        _buildSearchBar(),

        Expanded(
          child: _isLoading && _displayedPayments.isEmpty
              ? const Center(child: CircularProgressIndicator())
              : _displayedPayments.isEmpty
                  ? EmptyStateWidget(
                      icon: Icons.payment,
                      title: 'No Payments Yet',
                      message: 'No payments have been recorded yet.',
                      buttonText: 'Record Payment',
                      onButtonPressed: () {
                        PaymentsScreen.showCustomerSelectionForPayment(context);
                      },
                    )
                  : _buildScrollableContent(),
        ),
      ],
    );
  }

  Widget _buildPaymentSummary() {
    // Calculate totals
    double totalAmount = 0;
    double billPayments = 0;
    double creditPayments = 0;
    int paymentCount = _displayedPayments.length;

    // Only calculate totals if there are payments
    if (paymentCount > 0) {
      for (var payment in _displayedPayments) {
        totalAmount += payment.amount;
        if (payment.billId > 0) {
          billPayments += payment.amount;
        } else {
          creditPayments += payment.amount;
        }
      }
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Payment Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple.shade900,
                  fontSize: 18,
                ),
              ),
              Text(
                '$paymentCount payments',
                style: TextStyle(
                  color: Colors.purple.shade800,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.purple.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Colors.purple.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Payments',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.purple.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      CurrencyService.formatCurrency(totalAmount),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                        color: Colors.purple.shade700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Detailed stats
          Row(
            children: [
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Bill Payments',
                  value: CurrencyService.formatCurrency(billPayments),
                  icon: Icons.receipt,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Credit',
                  value: CurrencyService.formatCurrency(creditPayments),
                  icon: Icons.savings,
                  iconColor: Colors.indigo.shade700,
                  bgColor: Colors.indigo.shade50,
                  borderColor: Colors.indigo.shade200,
                  textColor: Colors.indigo.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color textColor,
    required Color borderColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: textColor,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search payments...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                    contentPadding: const EdgeInsets.symmetric(vertical: 10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: Colors.purple.shade400, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Sort button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: IconButton(
                  icon: Stack(
                    alignment: Alignment.center,
                    children: [
                      const Icon(Icons.sort),
                      if (_isSortActive)
                        Positioned(
                          right: 0,
                          bottom: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.purple,
                            ),
                          ),
                        ),
                    ],
                  ),
                  tooltip: 'Sort: ${_currentSortOption.label}',
                  onPressed: _showSortDialog,
                ),
              ),
            ],
          ),

          // Search results indicator
          if (_searchController.text.isNotEmpty)
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
              child: Row(
                children: [
                  Text(
                    'Found ${_displayedPayments.length} results',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      _searchController.clear();
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: const Color(0xFF2E7D32),
                    ),
                    child: const Text('Clear Search'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildScrollableContent() {
    return RefreshIndicator(
      onRefresh: _refreshPayments,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Customer credit info if a specific customer is selected
          if (widget.selectedCustomer != null)
            SliverToBoxAdapter(
              child: _buildCustomerCreditInfo(),
            ),

          // Payment summary section
          SliverToBoxAdapter(
            child: _buildPaymentSummary(),
          ),

          // Payment list
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index >= _displayedPayments.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final payment = _displayedPayments[index];
                final customer = _customersMap[payment.customerId];

                return _PaymentListItem(
                  payment: payment,
                  customer: customer,
                  onDelete: _deletePayment,
                  onPaymentUpdated: _refreshPayments,
                  isSelectionMode: _isSelectionMode,
                  isSelected: _selectedPaymentIds.contains(payment.id),
                  onToggleSelection: () => _togglePaymentSelection(payment.id),
                  onLongPress: () => _enterSelectionMode(payment.id),
                );
              },
              childCount: _displayedPayments.length + (_hasMoreItems ? 1 : 0),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildCustomerCreditInfo() {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with customer name and action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.person, color: Colors.purple.shade700),
                  const SizedBox(width: 8),
                  Text(
                    widget.selectedCustomer!.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.purple.shade900,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  // Refresh button
                  GestureDetector(
                    onTap: _loadCustomerCredit,
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Icon(Icons.refresh,
                          size: 16, color: Colors.blue.shade700),
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (_customerCredit > 0)
                    GestureDetector(
                      onTap: _showResetCreditDialog,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.refresh,
                                size: 14, color: Colors.red.shade700),
                            const SizedBox(width: 4),
                            Text(
                              'Reset Credit',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Credit amount display
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.purple.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Colors.purple.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Available Credit',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.purple.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _isLoadingCredit
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            CurrencyService.formatCurrency(_customerCredit,
                                decimalPlaces: 2),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _customerCredit > 0
                                  ? Colors.green.shade700
                                  : Colors.grey.shade700,
                              fontSize: 24,
                            ),
                          ),
                        ],
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showResetCreditDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Credit Balance',
            style: TextStyle(color: Colors.red.shade700)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to reset the credit balance for this customer?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This will permanently set the credit balance to zero. This action cannot be undone.',
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Customer: ${widget.selectedCustomer!.name}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              'Current Credit: ${CurrencyService.formatCurrency(_customerCredit)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => _resetCustomerCredit(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('RESET CREDIT'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetCustomerCredit() async {
    Navigator.of(context).pop(); // Close dialog

    setState(() {
      _isLoadingCredit = true;
    });

    try {
      await PaymentService.resetCustomerCredit(widget.selectedCustomer!.id);

      if (!mounted) return;
      setState(() {
        _customerCredit = 0;
        _isLoadingCredit = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Credit balance has been reset to zero')),
      );
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoadingCredit = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to reset credit: $e')),
      );
    }
  }

  void _togglePaymentSelection(int paymentId) {
    setState(() {
      if (_selectedPaymentIds.contains(paymentId)) {
        _selectedPaymentIds.remove(paymentId);
        if (_selectedPaymentIds.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedPaymentIds.add(paymentId);
      }
    });
    // Force rebuild to update the UI immediately
    if (mounted) setState(() {});
  }

  void _enterSelectionMode(int paymentId) {
    setState(() {
      _isSelectionMode = true;
      _selectedPaymentIds.add(paymentId);
    });
    // Force rebuild to update the UI immediately
    if (mounted) setState(() {});
  }

  // Method to handle deleting multiple payments
  Future<void> _deleteSelectedPayments() async {
    // Show confirmation dialog
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete ${_selectedPaymentIds.length} Payments',
            style: TextStyle(color: Colors.red.shade700)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete ${_selectedPaymentIds.length} selected payments?',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. Payments linked to bills will update the bill status.',
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (shouldDelete != true) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // Process each payment individually since some may be linked to bills
      for (final paymentId in _selectedPaymentIds.toList()) {
        final payment = _displayedPayments.firstWhere((p) => p.id == paymentId);

        if (payment.billId > 0) {
          // For payments linked to bills, use service to handle bill status updates
          await PaymentService.deletePaymentAndUpdateBillStatus(payment);
        } else {
          // For regular payments, just delete
          await DatabaseService.deletePayment(payment.id);

          // Notify other parts of the app that payment data has changed
          DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);

          // If payment was linked to a bill, notify about bill changes too
          if (payment.billId > 0) {
            DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
          }

          // Also notify about customer changes since balance is affected
          DataChangeNotifierService()
              .notifyDataChanged(DataChangeType.customer);
        }
      }

      // Store the count before clearing the selection
      final deletedCount = _selectedPaymentIds.length;

      // Clear selection mode
      setState(() {
        _isSelectionMode = false;
        _selectedPaymentIds.clear();
      });

      // Reload payments
      await _refreshPayments();

      // Check if widget is still mounted before using context
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$deletedCount payments deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Check if widget is still mounted before using context
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting payments: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }


}

// Extract payment item to a separate widget to reduce rebuilds
class _PaymentListItem extends StatelessWidget {
  final Payment payment;
  final Customer? customer;
  final Function(Payment) onDelete;
  final VoidCallback onPaymentUpdated;
  final bool isSelectionMode;
  final bool isSelected;
  final VoidCallback onToggleSelection;
  final VoidCallback onLongPress;

  const _PaymentListItem({
    required this.payment,
    required this.customer,
    required this.onDelete,
    required this.onPaymentUpdated,
    this.isSelectionMode = false,
    this.isSelected = false,
    required this.onToggleSelection,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final formattedDate = DateFormat('dd MMM yyyy').format(payment.paymentDate);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected
                ? Colors.amber.shade500
                : payment.billId > 0
                    ? Colors.green.shade400
                    : Colors.purple.shade400,
            width: isSelected ? 2.0 : 1.5,
          ),
        ),
        color: isSelected ? Colors.amber.shade50 : null,
        child: InkWell(
          onTap: isSelectionMode
              ? () {
                  onToggleSelection();
                  // Provide haptic feedback when selecting
                  HapticFeedback.selectionClick();
                }
              : () => _showPaymentDetails(context),
          onLongPress: isSelectionMode
              ? null
              : () {
                  onLongPress();
                  // Provide haptic feedback when entering selection mode
                  HapticFeedback.heavyImpact();
                },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Payment ID and Customer name
                Row(
                  children: [
                    // Selection checkbox when in selection mode
                    if (isSelectionMode) ...[
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) => onToggleSelection(),
                        activeColor: Colors.amber.shade700,
                        checkColor: Colors.white,
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Payment ID with icon
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 5),
                      decoration: BoxDecoration(
                        color: Colors.indigo.shade50,
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: Colors.indigo.shade200),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.payments,
                              size: 14, color: Colors.indigo.shade700),
                          const SizedBox(width: 3),
                          Text(
                            "#${payment.id.toString().padLeft(3, '0')}",
                            style: TextStyle(
                              color: Colors.indigo.shade800,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Customer name with icon
                    Expanded(
                      child: GestureDetector(
                        onTap: customer != null
                            ? () {
                                // Navigate to customer details if needed
                              }
                            : null,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 5),
                          decoration: BoxDecoration(
                            color: Colors.teal.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.teal.shade200),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person,
                                color: Colors.teal.shade700,
                                size: 14,
                              ),
                              const SizedBox(width: 3),
                              Expanded(
                                child: Text(
                                  customer?.name ?? 'Unknown',
                                  style: TextStyle(
                                    color: Colors.teal.shade800,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button in row 1 (only when not in selection mode)
                    if (!isSelectionMode)
                      GestureDetector(
                        onTap: () => _editPayment(context),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.cyan.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.cyan.shade200),
                          ),
                          child: Icon(Icons.edit,
                              color: Colors.cyan.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Row 2: Amount and delete
                Row(
                  children: [
                    // Date with icon and label
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 14, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Use AmountChip for consistency with transaction screen
                    Expanded(
                      child: AmountChip(
                        amount: payment.amount,
                        showLabel: false,
                        customColor: payment.billId > 0
                            ? Colors.green.shade700
                            : Colors.indigo.shade700,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    if (!isSelectionMode)
                      GestureDetector(
                        onTap: () => onDelete(payment),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Icon(Icons.delete,
                              color: Colors.red.shade700, size: 12),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Extract all bill IDs from payment remarks and payment allocations
  Future<List<int>> _getAllLinkedBillIds(BuildContext context) async {
    final List<int> billIds = [];

    // Add the primary billId if it's valid
    if (payment.billId > 0) {
      billIds.add(payment.billId);
    }

    // Get payment allocations from database service
    try {
      final allocations =
          await DatabaseService.getPaymentAllocationsByPaymentId(payment.id);
      for (var allocation in allocations) {
        if (allocation.billId > 0) {
          final billId = allocation.billId;
          if (!billIds.contains(billId)) {
            billIds.add(billId);
          }
        }
      }
    } catch (e) {
      // Debug: Error getting payment allocations: $e
    }

    // Extract additional bill IDs from remarks
    final remarks = payment.remarks?.toLowerCase() ?? '';

    // Look for patterns like "Rs X for bill #Y"
    RegExp billRegex = RegExp(r'bill #(\d+)|bill (\d+)|bill.*?(\d+)');
    final matches = billRegex.allMatches(remarks);

    for (final match in matches) {
      // Try to get the bill ID from any capturing group
      String? billIdStr;
      for (int i = 1; i <= match.groupCount; i++) {
        if (match.group(i) != null) {
          billIdStr = match.group(i);
          break;
        }
      }

      if (billIdStr != null) {
        final billId = int.tryParse(billIdStr);
        if (billId != null && billId > 0 && !billIds.contains(billId)) {
          billIds.add(billId);
        }
      }
    }

    return billIds;
  }

  // Get bill details using bill IDs
  Future<List<Bill>> _getPaymentBillDetails(BuildContext context) async {
    final billIds = await _getAllLinkedBillIds(context);
    final bills = <Bill>[];

    for (final billId in billIds) {
      try {
        final bill = await DatabaseService.getBillWithPaymentStatus(billId);
        if (bill != null) {
          bills.add(bill);
        }
      } catch (e) {
        // Debug: Error getting bill details: $e
      }
    }

    return bills;
  }

  void _showPaymentDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Customer info
              Row(
                children: [
                  Icon(Icons.person, color: Colors.green.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Customer:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      customer?.name ?? 'Unknown Customer',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment ID row
              Row(
                children: [
                  Icon(Icons.payments, color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Payment #${payment.id}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Payment info
              Row(
                children: [
                  Icon(Icons.payments, color: Colors.indigo.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Amount:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Rs ${payment.amount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment date
              Row(
                children: [
                  Icon(Icons.calendar_today,
                      color: Colors.purple.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Date:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    DateFormat('dd MMM yyyy').format(payment.paymentDate),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Payment method
              Row(
                children: [
                  Icon(_getPaymentMethodIcon(),
                      color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Method:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    payment.paymentMethod ?? 'Unknown',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Bills section
              FutureBuilder<List<Bill>>(
                future: _getPaymentBillDetails(context),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(strokeWidth: 2),
                    );
                  }

                  final bills = snapshot.data ?? [];

                  if (bills.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.teal.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.teal.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.account_balance_wallet,
                              color: Colors.teal.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Credit',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.teal.shade800,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.receipt_long,
                                color: Color(0xFF2E7D32), size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Applied to ${bills.length} ${bills.length == 1 ? 'Bill' : 'Bills'}:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ...bills.map((bill) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: InkWell(
                                onTap: () {
                                  if (context.mounted) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => BillDetailsScreen(
                                          bill: bill,
                                          customer: customer,
                                        ),
                                      ),
                                    );
                                  }
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: bill.isPaid
                                        ? Colors.green.shade50
                                        : bill.isPartiallyPaid
                                            ? Colors.blue.shade50
                                            : Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: bill.isPaid
                                          ? Colors.green.shade300
                                          : bill.isPartiallyPaid
                                              ? Colors.blue.shade300
                                              : Colors.red.shade300,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.receipt,
                                              color: bill.isPaid
                                                  ? const Color(0xFF2E7D32)
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade800
                                                      : Colors.red.shade800,
                                              size: 16),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Bill #${bill.id}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: bill.isPaid
                                                  ? const Color(0xFF2E7D32)
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade800
                                                      : Colors.red.shade800,
                                            ),
                                          ),
                                          const Spacer(),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: bill.isPaid
                                                  ? Colors.green.shade100
                                                  : bill.isPartiallyPaid
                                                      ? Colors.blue.shade100
                                                      : Colors.red.shade100,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Text(
                                              bill.isPaid
                                                  ? 'Paid'
                                                  : bill.isPartiallyPaid
                                                      ? 'Partial'
                                                      : 'Unpaid',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: bill.isPaid
                                                    ? Colors.green.shade800
                                                    : bill.isPartiallyPaid
                                                        ? Colors.blue.shade800
                                                        : Colors.red.shade800,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 6),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Amount: Rs ${bill.amount.toStringAsFixed(0)}',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade800,
                                            ),
                                          ),
                                          if (bill.isPartiallyPaid &&
                                              bill.partialAmount != null)
                                            Text(
                                              'Paid: Rs ${bill.partialAmount!.toStringAsFixed(0)}',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.blue.shade800,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          'Date: ${DateFormat('dd MMM yyyy').format(bill.billDate)}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )),
                      ],
                    );
                  }
                },
              ),

              // Remarks section
              if (payment.remarks != null && payment.remarks!.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                Text(
                  'Remarks:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    payment.remarks!,
                    style: TextStyle(
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editPayment(BuildContext context) {
    // Navigate to edit payment screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AutoPaymentFormScreen(
          customer: customer!,
          existingPayment: payment,
        ),
      ),
    ).then((result) {
      // If payment was updated successfully, refresh the list
      if (result == true) {
        onPaymentUpdated();
      }
    });
  }

  IconData _getPaymentMethodIcon() {
    final method = payment.paymentMethod?.toLowerCase() ?? '';

    switch (method) {
      case 'cash':
        return Icons.money;
      case 'bank transfer':
        return Icons.account_balance;
      case 'check':
        return Icons.payment;
      default:
        return Icons.payment;
    }
  }
}
