class Payment {
  int id;
  int customerId;
  int billId; // Default to 0 for credit-only payments
  DateTime paymentDate;
  double amount;
  String? paymentMethod;
  String? remarks;

  // Named constructor with required parameters
  Payment.create({
    required this.customerId,
    required this.billId,
    required this.paymentDate,
    required this.amount,
    this.paymentMethod,
    this.remarks,
  }) : id = 0;

  // Default constructor
  Payment({
    this.id = 0,
    required this.customerId,
    this.billId = 0,
    required this.paymentDate,
    required this.amount,
    this.paymentMethod,
    this.remarks,
  });

  // Convert a Payment object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'customerId': customerId,
      'billId': billId,
      'paymentDate': paymentDate.toIso8601String(),
      'amount': amount,
      'paymentMethod': paymentMethod,
      'remarks': remarks,
    };
  }

  // Create a Payment object from a Map
  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id'],
      customerId: map['customerId'],
      billId: map['billId'],
      paymentDate: DateTime.parse(map['paymentDate']),
      amount: map['amount'],
      paymentMethod: map['paymentMethod'],
      remarks: map['remarks'],
    );
  }

  // Clone method for creating a copy of the payment
  Payment clone() {
    return Payment(
      id: id,
      customerId: customerId,
      billId: billId,
      paymentDate: paymentDate,
      amount: amount,
      paymentMethod: paymentMethod,
      remarks: remarks,
    );
  }

  // Check if this payment is a credit payment (not linked to a specific bill)
  bool get isCreditPayment => billId == 0;

  // Check if this payment is associated with a bill
  bool get hasBill => billId > 0;
}
