import 'dart:io';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_utility_service.dart';

class PdfCustomerListService {
  static Future<File> generateCustomerListPdf({
    required List<Customer> customers,
    Map<String, dynamic>? summary,
    PdfSettings? settings,
  }) async {
    try {
      // Use provided settings or create default modern settings
      final pdfSettings = settings ?? PdfSettings.modern().copyWith(
        additionalSettings: {
          'footerText': 'Customer Directory',
        },
      );

      // Define colors from settings
      final primaryColor = pdfSettings.primaryColor;
      final accentColor = pdfSettings.accentColor;
      final textColor = pdfSettings.textColor;

      // Create a PDF document with template
      final pdf = await PdfTemplateService.createPdfWithTemplate(
        title: 'CUSTOMER DIRECTORY',
        subtitle: 'Customer List',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        watermarkText: pdfSettings.showWatermark ? (pdfSettings.watermarkText ?? 'COPY') : null,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      // Create content widgets
      final List<pw.Widget> contentWidgets = [
        // Section title
        PdfTemplateService.createSectionTitle(
          'CUSTOMER LIST',
          color: primaryColor,
          fontSize: 16,
        ),
        pw.SizedBox(height: 10),

        // Summary Section
        if (summary != null) ...[
          pw.Container(
            padding: const pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              color: PdfBaseService.lightGreen,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
            ),
            child: pw.Row(
              children: [
                pw.Expanded(
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Total Customers:',
                          style: pw.TextStyle(font: PdfBaseService.boldFont, fontSize: 11)),
                      pw.Text('${summary['totalCount'] ?? customers.length}',
                          style: pw.TextStyle(font: PdfBaseService.boldFont, fontSize: 16)),
                    ],
                  ),
                ),
                pw.Expanded(
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Active Customers:',
                          style: pw.TextStyle(font: PdfBaseService.boldFont, fontSize: 11)),
                      pw.Text('${summary['activeCount'] ?? ''}',
                          style: pw.TextStyle(font: PdfBaseService.boldFont, fontSize: 16)),
                    ],
                  ),
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 20),
        ],

        // Customer Table
        PdfTemplateService.createSectionTitle('CUSTOMER DETAILS', color: primaryColor),
        pw.Table(
          border: pw.TableBorder.all(
            color: PdfColor.fromHex('#CCCCCC'),
            width: 0.5,
          ),
          columnWidths: {
            0: const pw.FixedColumnWidth(40), // ID
            1: const pw.FlexColumnWidth(3), // Name
            2: const pw.FlexColumnWidth(2), // Contact
            3: const pw.FixedColumnWidth(80), // Balance
            4: const pw.FixedColumnWidth(60), // Status
          },
          children: [
            pw.TableRow(
              decoration: pw.BoxDecoration(
                color: PdfBaseService.lightBlue,
              ),
                  children: [
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(5),
                      child: pw.Text('ID',
                          style: pw.TextStyle(font: PdfBaseService.boldFont, fontSize: 10)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(5),
                      child: pw.Text('Name',
                          style: pw.TextStyle(font: PdfBaseService.boldFont, fontSize: 10)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(5),
                      child: pw.Text('Contact',
                          style: pw.TextStyle(font: PdfBaseService.boldFont, fontSize: 10)),
                    ),
                  ],
                ),
                ...customers.map((customer) => pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text('#${customer.id}',
                              style: pw.TextStyle(
                                  font: PdfBaseService.regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(customer.name,
                              style: pw.TextStyle(
                                  font: PdfBaseService.regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(customer.contactNumber ?? 'N/A',
                              style: pw.TextStyle(
                                  font: PdfBaseService.regularFont, fontSize: 9)),
                        ),
                      ],
                    )),
              ],
            ),

            // Footer
            pw.SizedBox(height: 30),
            PdfBaseService.buildFooter(),
          ];

      // Add page with template using settings
      await PdfTemplateService.addPageWithTemplate(
        pdf: pdf,
        contentWidgets: contentWidgets,
        title: 'CUSTOMER DIRECTORY',
        subtitle: 'Customer List',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        contentStyle: pdfSettings.contentStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      // Save the PDF file
      final fileName = 'customer_list_${DateFormat('yyyyMMdd').format(DateTime.now())}.pdf';
      return await PdfUtilityService.savePdfToTemp(pdf, fileName);
    } catch (e) {
      rethrow;
    }
  }
}
