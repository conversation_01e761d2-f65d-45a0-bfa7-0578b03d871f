import 'dart:io';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_utility_service.dart';

class PdfCustomerDetailService {
  static Future<File> generateCustomerTransactionsPdf({
    required Customer customer,
    List<Bill>? bills,
    List<Payment>? payments,
    DateTime? startDate,
    DateTime? endDate,
    PdfSettings? settings,
  }) async {
    try {
      // Use provided settings or create default modern settings
      final pdfSettings = settings ?? PdfSettings.modern().copyWith(
        primaryColor: PdfColor.fromHex('#2E7D32'), // Green
        accentColor: PdfColor.fromHex('#1565C0'), // Blue
        additionalSettings: {
          'footerText': '${customer.name} - Customer Statement',
        },
      );

      // Define colors from settings
      final primaryColor = pdfSettings.primaryColor;
      final accentColor = pdfSettings.accentColor;
      final textColor = pdfSettings.textColor;
      final lightGreen = PdfBaseService.lightGreen;
      final lightBlue = PdfBaseService.lightBlue;
      final lightRed = PdfBaseService.lightRed;
      final paidColor = PdfBaseService.paidColor;
      final partialColor = PdfBaseService.partialColor;
      final unpaidColor = PdfBaseService.unpaidColor;

      // Load fonts
      await PdfBaseService.loadFonts();
      final regularFont = PdfBaseService.regularFont;
      final boldFont = PdfBaseService.boldFont;
      // italicFont is loaded but not used in this method

      // Create a PDF document with template
      final pdf = await PdfTemplateService.createPdfWithTemplate(
        title: 'CUSTOMER STATEMENT',
        subtitle: customer.name,
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        watermarkText: pdfSettings.showWatermark ? (pdfSettings.watermarkText ?? 'COPY') : null,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      // Calculate summary data
      double totalBillAmount = 0;
      double totalPaidAmount = 0;
      double totalOutstandingAmount = 0;
      int paidBillsCount = 0;
      int partialBillsCount = 0;
      int unpaidBillsCount = 0;

      if (bills != null) {
        for (var bill in bills) {
          totalBillAmount += bill.amount;

          if (bill.isPaid) {
            totalPaidAmount += bill.amount;
            paidBillsCount++;
          } else if (bill.isPartiallyPaid) {
            totalPaidAmount += (bill.partialAmount ?? 0);
            totalOutstandingAmount += bill.outstandingAmount;
            partialBillsCount++;
          } else {
            totalOutstandingAmount += bill.amount;
            unpaidBillsCount++;
          }
        }
      }

      // Create content widgets
      final List<pw.Widget> contentWidgets = [
        // Date range if provided
        if (startDate != null || endDate != null)
          pw.Text(
              'Period: ${startDate != null ? DateFormat('dd/MM/yyyy').format(startDate) : 'Start'} - ${endDate != null ? DateFormat('dd/MM/yyyy').format(endDate) : 'End'}',
              style: pw.TextStyle(font: regularFont, fontSize: 10)),
        pw.SizedBox(height: 20),

            // Customer Information
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: lightBlue,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('CUSTOMER INFORMATION',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 14,
                        color: primaryColor,
                      )),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Name:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text(customer.name,
                                style: pw.TextStyle(font: regularFont, fontSize: 14)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Contact:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text(customer.contactNumber ?? 'N/A',
                                style: pw.TextStyle(font: regularFont, fontSize: 14)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Balance:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text(
                                'Rs. ${customer.balance.toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                  font: boldFont,
                                  fontSize: 14,
                                  color: customer.balance < 0 ? unpaidColor : (customer.balance > 0 ? paidColor : null),
                                )),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Status:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text(
                                customer.balance < 0 ? 'OUTSTANDING' : (customer.balance > 0 ? 'CREDIT' : 'BALANCED'),
                                style: pw.TextStyle(
                                  font: boldFont,
                                  fontSize: 14,
                                  color: customer.balance < 0 ? unpaidColor : (customer.balance > 0 ? paidColor : null),
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // Account Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: lightGreen,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('ACCOUNT SUMMARY',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 14,
                        color: primaryColor,
                      )),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Bills:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('${bills?.length ?? 0}',
                                style: pw.TextStyle(font: boldFont, fontSize: 14)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Amount:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${totalBillAmount.toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: boldFont,
                                    fontSize: 14,
                                    color: primaryColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Paid Amount:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${totalPaidAmount.toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: regularFont,
                                    fontSize: 12,
                                    color: paidColor)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Outstanding:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${totalOutstandingAmount.toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: regularFont,
                                    fontSize: 12,
                                    color: unpaidColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Row(
                          children: [
                            pw.Container(
                              width: 12,
                              height: 12,
                              decoration: pw.BoxDecoration(
                                color: paidColor,
                                shape: pw.BoxShape.circle,
                              ),
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text('Paid: $paidBillsCount',
                                style: pw.TextStyle(font: regularFont, fontSize: 10)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Row(
                          children: [
                            pw.Container(
                              width: 12,
                              height: 12,
                              decoration: pw.BoxDecoration(
                                color: partialColor,
                                shape: pw.BoxShape.circle,
                              ),
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text('Partial: $partialBillsCount',
                                style: pw.TextStyle(font: regularFont, fontSize: 10)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Row(
                          children: [
                            pw.Container(
                              width: 12,
                              height: 12,
                              decoration: pw.BoxDecoration(
                                color: unpaidColor,
                                shape: pw.BoxShape.circle,
                              ),
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text('Unpaid: $unpaidBillsCount',
                                style: pw.TextStyle(font: regularFont, fontSize: 10)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // Bills Table
            if (bills != null && bills.isNotEmpty) ...[
              pw.Text('BILL HISTORY',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: primaryColor,
                  )),
              pw.Divider(color: primaryColor),
              pw.Table(
                border: pw.TableBorder.all(
                  color: PdfColor.fromHex('#CCCCCC'),
                  width: 0.5,
                ),
                columnWidths: {
                  0: const pw.FixedColumnWidth(30), // ID
                  1: const pw.FixedColumnWidth(70), // Date
                  2: const pw.FixedColumnWidth(60), // Duration
                  3: const pw.FixedColumnWidth(60), // Amount
                  4: const pw.FixedColumnWidth(60), // Paid
                  5: const pw.FixedColumnWidth(50), // Status
                },
                children: [
                  // Table header
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      color: lightGreen,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('ID',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Date',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Duration',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Amount',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Paid',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Status',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                    ],
                  ),
                  // Table rows
                  ...bills.map((bill) {
                    final paidAmount = bill.isPaid ? bill.amount : (bill.partialAmount ?? 0);

                    // Determine status color
                    final PdfColor statusColor = bill.isPaid
                        ? paidColor
                        : (bill.isPartiallyPaid ? partialColor : unpaidColor);

                    // Determine status text
                    final String billStatus = bill.isPaid
                        ? 'PAID'
                        : (bill.isPartiallyPaid ? 'PARTIAL' : 'UNPAID');

                    return pw.TableRow(
                      decoration: pw.BoxDecoration(
                        color: bill.isPaid
                            ? PdfBaseService.colorWithOpacity(lightGreen, 0.2)
                            : (bill.isPartiallyPaid
                                ? PdfBaseService.colorWithOpacity(lightBlue, 0.2)
                                : PdfBaseService.colorWithOpacity(lightRed, 0.2)),
                      ),
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text('#${bill.id}',
                              style: pw.TextStyle(font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              DateFormat('dd/MM/yyyy').format(bill.billDate),
                              style: pw.TextStyle(font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              '${bill.durationHoursWhole}h ${bill.durationMinutes}m',
                              style: pw.TextStyle(font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              'Rs. ${bill.amount.toStringAsFixed(2)}',
                              style: pw.TextStyle(font: boldFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              'Rs. ${paidAmount.toStringAsFixed(2)}',
                              style: pw.TextStyle(
                                  font: boldFont, fontSize: 9,
                                  color: paidAmount > 0 ? paidColor : null)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(billStatus,
                              style: pw.TextStyle(
                                  font: boldFont,
                                  fontSize: 9,
                                  color: statusColor)),
                        ),
                      ],
                    );
                  }),
                ],
              ),
            ],

      ];

      // Add page with template using settings
      await PdfTemplateService.addPageWithTemplate(
        pdf: pdf,
        contentWidgets: PdfTemplateService.createContentSection(
          contentStyle: pdfSettings.contentStyle,
          contentWidgets: contentWidgets,
          primaryColor: primaryColor,
          accentColor: accentColor,
          textColor: textColor,
        ),
        title: 'CUSTOMER STATEMENT',
        subtitle: customer.name,
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        contentStyle: pdfSettings.contentStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        headerData: {
          ...pdfSettings.getHeaderData(),
          'customerInfo': 'Customer #${customer.id}',
          'period': startDate != null || endDate != null
              ? 'Period: ${startDate != null ? DateFormat('dd/MM/yyyy').format(startDate) : 'Start'} - ${endDate != null ? DateFormat('dd/MM/yyyy').format(endDate) : 'End'}'
              : null,
        },
        footerData: pdfSettings.getFooterData(),
      );

      // Save the PDF file
      final fileName = 'customer_${customer.id}_${DateFormat('yyyyMMdd').format(DateTime.now())}.pdf';
      return await PdfUtilityService.savePdfToTemp(pdf, fileName);
    } catch (e) {
      rethrow;
    }
  }
}
