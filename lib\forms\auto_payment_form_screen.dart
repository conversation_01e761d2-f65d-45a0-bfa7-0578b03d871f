import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/services/payment_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/message_dialog.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';

class AutoPaymentFormScreen extends StatefulWidget {
  final Customer customer;
  final Payment? existingPayment;

  const AutoPaymentFormScreen({
    super.key,
    required this.customer,
    this.existingPayment,
  });

  @override
  State<AutoPaymentFormScreen> createState() => _AutoPaymentFormScreenState();
}

class _AutoPaymentFormScreenState extends State<AutoPaymentFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _remarksController = TextEditingController();

  DateTime _paymentDate = DateTime.now();
  String _paymentMethod = 'Cash';
  bool _isLoading = false;
  double _creditBalance = 0;

  final List<String> _paymentMethods = [
    'Cash',
    'Bank Transfer',
    'Check',
    'UPI',
    'Other'
  ];

  @override
  void initState() {
    super.initState();
    _loadCustomerCredit();

    // Initialize with existing payment data if editing
    if (widget.existingPayment != null) {
      _paymentDate = widget.existingPayment!.paymentDate;
      _amountController.text = widget.existingPayment!.amount.toString();
      _paymentMethod = widget.existingPayment!.paymentMethod ?? 'Cash';
      if (widget.existingPayment!.remarks != null) {
        _remarksController.text = widget.existingPayment!.remarks!;
      }
    }
  }

  Future<void> _loadCustomerCredit() async {
    final creditBalance =
        await PaymentService.getCustomerCreditBalance(widget.customer.id);
    setState(() {
      _creditBalance = creditBalance;
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _paymentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _paymentDate) {
      setState(() {
        _paymentDate = picked;
      });
    }
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.existingPayment != null) {
        // Update existing payment
        final updatedPayment = widget.existingPayment!.clone();
        updatedPayment.amount = double.parse(_amountController.text);
        updatedPayment.paymentDate = _paymentDate;
        updatedPayment.paymentMethod = _paymentMethod;
        updatedPayment.remarks =
            _remarksController.text.isEmpty ? null : _remarksController.text;

        await PaymentService.updatePayment(updatedPayment);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Payment updated successfully')),
          );
          NavigationHelper.goBack(context, true);
        }
      } else {
        // Process new payment with automatic allocation
        final result = await PaymentService.processPayment(
          customerId: widget.customer.id,
          amount: double.parse(_amountController.text),
          paymentDate: _paymentDate,
          paymentMethod: _paymentMethod,
          remarks:
              _remarksController.text.isEmpty ? null : _remarksController.text,
        );

        if (result.containsKey('success') && result['success'] == true) {
          if (mounted) {
            // Show success message with allocation details
            _showPaymentSuccessDialog(result);
          }
        } else {
          // Handle error case when success is false
          String errorMessage = 'Failed to process payment';
          if (result.containsKey('error')) {
            errorMessage = result['error'] as String;
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(errorMessage)),
            );
          }
        }
      }
    } catch (e) {
      // Error will be shown in the UI
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error processing payment: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showPaymentSuccessDialog(Map<String, dynamic> result) {
    if (!mounted) return;

    final allocations = result['allocations'] as List<dynamic>;
    final usedAmount = result['usedAmount'] as double;
    final remainingCredit = result['remainingCredit'] as double;

    // Get payment ID safely from the list of payment IDs
    int? paymentId;
    if (result.containsKey('paymentIds') &&
        result['paymentIds'] is List &&
        (result['paymentIds'] as List).isNotEmpty) {
      final paymentIds = result['paymentIds'] as List;
      paymentId = paymentIds.first as int;
    }

    // Get updated remarks text from the already-created payment
    if (mounted) {
      setState(() {
        // The remarks were already set in the payment service
        _remarksController.text = '';
      });
    }

    // Use a local variable to track if dialog is completed
    bool isDialogCompleted = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Payment Successful'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Show payment ID if available
              if (paymentId != null)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.receipt_long,
                          size: 16, color: Colors.green.shade700),
                      const SizedBox(width: 8),
                      Text(
                        'Payment #$paymentId',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade900,
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 12),

              Text(
                'Amount: ${CurrencyService.formatCurrency(double.parse(_amountController.text))}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (_creditBalance > 0)
                Text(
                  'Used ${CurrencyService.formatCurrency(_creditBalance)} from existing credit.',
                  style: const TextStyle(color: Colors.blue),
                ),
              const SizedBox(height: 16),
              const Text('Payment Allocation:'),
              const SizedBox(height: 8),
              ...allocations.map((allocation) {
                final status = allocation['status'] as String;
                final amount = allocation['amount'] as double;
                final billId = allocation['billId'] as int?;
                final date = allocation['date'] as DateTime?;

                if (status == 'CREDIT' || billId == null || billId == 0) {
                  return ListTile(
                    dense: true,
                    leading: const Icon(Icons.account_balance_wallet,
                        color: Colors.green),
                    title: const Text('Saved to Credit'),
                    subtitle: Text(
                      CurrencyService.formatCurrency(amount),
                    ),
                  );
                } else {
                  return ListTile(
                    dense: true,
                    leading: Icon(
                      status == 'PAID' ? Icons.check_circle : Icons.pending,
                      color: status == 'PAID' ? Colors.green : Colors.blue,
                    ),
                    title: Text(
                        'Bill #$billId ${date != null ? '(${DateFormat('dd MMM yyyy').format(date)})' : ''}'),
                    trailing: Text(
                      '${CurrencyService.formatCurrency(amount)} - ${status == 'PAID' ? 'Fully Paid' : 'Partially Paid'}',
                      style: TextStyle(
                        color: status == 'PAID'
                            ? Colors.green.shade700
                            : Colors.blue.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  );
                }
              }),
              const Divider(),
              Text(
                'Total Applied: ${CurrencyService.formatCurrency(usedAmount)}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (remainingCredit > 0)
                Text(
                  'Remaining Credit: ${CurrencyService.formatCurrency(remainingCredit)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              const SizedBox(height: 16),
              const Text(
                'Note: Payment details have been automatically recorded.',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () async {
              isDialogCompleted = true;
              // Close dialog using dialog context with smooth transition
              Navigator.of(dialogContext).pop();

              // Show message dialog if we have a payment ID
              if (paymentId != null && mounted) {
                // Get the payment details
                final payment = await PaymentService.getPaymentById(paymentId);
                if (payment != null && mounted) {
                  await showDialog(
                    context: context,
                    builder: (context) => MessageDialog(
                      customer: widget.customer,
                      payment: payment,
                      remainingCredit: remainingCredit,
                      title: 'Send Payment Confirmation',
                    ),
                  );
                }
              }

              if (mounted) {
                // Return to previous screen with smooth transition
                NavigationHelper.goBack(context, true);
              }
            },
            child: const Text('OK'),
          ),
        ],
      ),
    ).then((_) {
      // If dialog was closed by system (back button, etc.) and not by our button
      if (!isDialogCompleted && mounted) {
        // Ensure we properly return to previous screen with smooth transition
        NavigationHelper.goBack(context, true);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.existingPayment != null;

    return Scaffold(
      appBar: TubewellAppBar(
        title: isEditing ? 'Edit Payment' : 'Record Payment',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer Info
                    Card(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.person, color: Colors.blue.shade700),
                                const SizedBox(width: 8),
                                Text(
                                  'Customer Details',
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Divider(height: 1, thickness: 1),
                            const SizedBox(height: 12),
                            Text(
                              widget.customer.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                            if (widget.customer.contactNumber != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Row(
                                  children: [
                                    Icon(Icons.phone,
                                        color: Colors.green.shade700, size: 16),
                                    const SizedBox(width: 8),
                                    Text(
                                      widget.customer.contactNumber!,
                                      style: TextStyle(
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            if (_creditBalance > 0)
                              Container(
                                margin: const EdgeInsets.only(top: 16.0),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border:
                                      Border.all(color: Colors.green.shade200),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.account_balance_wallet,
                                        color: Colors.green.shade700, size: 16),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Available Credit: ${CurrencyService.formatCurrency(_creditBalance)}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                    // Payment Date
                    Card(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.calendar_today,
                                    color: Colors.purple.shade700),
                                const SizedBox(width: 8),
                                Text(
                                  'Payment Date',
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Divider(height: 1, thickness: 1),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Text(
                                  'Select Date:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const Spacer(),
                                TextButton.icon(
                                  onPressed: () => _selectDate(context),
                                  icon: Icon(Icons.calendar_today,
                                      color: Colors.purple.shade700),
                                  label: Text(
                                    DateFormat('dd MMM yyyy')
                                        .format(_paymentDate),
                                    style: TextStyle(
                                      color: Colors.purple.shade700,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.purple.shade50,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Payment Details
                    Card(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      elevation: 3,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.payment,
                                    color: Colors.green.shade700),
                                const SizedBox(width: 8),
                                Text(
                                  'Payment Details',
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Divider(height: 1, thickness: 1),
                            const SizedBox(height: 16),

                            // Amount
                            TextFormField(
                              controller: _amountController,
                              decoration: InputDecoration(
                                labelText: 'Amount',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(Icons.monetization_on,
                                    color: Colors.green.shade700),
                                prefixText:
                                    '${CurrencyService.currentCurrency.symbol} ',
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter an amount';
                                }
                                final amount = double.tryParse(value);
                                if (amount == null) {
                                  return 'Please enter a valid amount';
                                }
                                if (amount <= 0) {
                                  return 'Amount must be greater than zero';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Payment Method
                            DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                labelText: 'Payment Method',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(Icons.credit_card,
                                    color: Colors.blue.shade700),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                              ),
                              value: _paymentMethod,
                              items: _paymentMethods
                                  .map<DropdownMenuItem<String>>((method) {
                                return DropdownMenuItem<String>(
                                  value: method,
                                  child: Text(method),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _paymentMethod = value!;
                                });
                              },
                            ),
                            const SizedBox(height: 16),

                            // Remarks
                            TextFormField(
                              controller: _remarksController,
                              decoration: InputDecoration(
                                labelText: 'Remarks (Optional)',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(Icons.note,
                                    color: Colors.orange.shade700),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                helperText: !isEditing
                                    ? 'Payment allocation details will be added automatically'
                                    : null,
                                helperStyle: TextStyle(
                                  color: Colors.green.shade700,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                              maxLines: 2,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Process Payment Button
                    Row(
                      children: [
                        if (isEditing)
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red.shade700,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 16),
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                          ),
                        if (isEditing) const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _processPayment,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7D32),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Text(
                                isEditing
                                    ? 'Update Payment'
                                    : 'Process Payment',
                                style: const TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
