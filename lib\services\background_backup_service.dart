// TEMPORARILY DISABLED - WorkManager dependency commented out
/*
import 'dart:io';
import 'package:workmanager/workmanager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/settings_service.dart';

/// Background service for handling scheduled backups using WorkManager
class BackgroundBackupService {
  static const String _taskName = 'scheduled_backup_task';
  static const String _uniqueName = 'tubewell_scheduled_backup';
  static final Logger _logger = Logger();

  /// Initialize the background service
  static Future<void> initialize() async {
    try {
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: false, // Set to false for production
      );
      _logger.i('Background backup service initialized');
    } catch (e) {
      _logger.e('Failed to initialize background backup service: $e');
    }
  }

  /// Schedule a periodic backup task
  static Future<void> schedulePeriodicBackup({
    required int intervalHours,
  }) async {
    try {
      // Cancel any existing scheduled backup
      await cancelScheduledBackup();

      // Schedule new periodic backup
      await Workmanager().registerPeriodicTask(
        _uniqueName,
        _taskName,
        frequency: Duration(hours: intervalHours),
        constraints: Constraints(
          networkType: NetworkType.not_required,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: true,
        ),
        inputData: {
          'interval_hours': intervalHours,
          'task_type': 'periodic_backup',
        },
      );

      _logger.i('Scheduled periodic backup every $intervalHours hours');
    } catch (e) {
      _logger.e('Failed to schedule periodic backup: $e');
      throw Exception('Failed to schedule backup: $e');
    }
  }

  /// Cancel scheduled backup
  static Future<void> cancelScheduledBackup() async {
    try {
      await Workmanager().cancelByUniqueName(_uniqueName);
      _logger.i('Cancelled scheduled backup');
    } catch (e) {
      _logger.e('Failed to cancel scheduled backup: $e');
    }
  }

  /// Check if backup is currently scheduled
  static Future<bool> isBackupScheduled() async {
    try {
      // Note: WorkManager doesn't provide a direct way to check if a task is scheduled
      // We'll rely on SharedPreferences to track this
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('scheduled_backup_enabled') ?? false;
    } catch (e) {
      _logger.e('Failed to check backup schedule status: $e');
      return false;
    }
  }

  /// Perform the actual backup operation
  static Future<bool> performBackup() async {
    try {
      _logger.i('Starting background backup...');

      // Check storage permission
      final status = await Permission.storage.status;
      if (!status.isGranted) {
        _logger.w('Storage permission not granted for background backup');
        return false;
      }

      // Create backup file
      final backupFile = await _createBackupFile();
      if (!await backupFile.exists()) {
        _logger.e('Failed to create backup file');
        return false;
      }

      // Get downloads directory
      Directory? downloadsDir;
      if (Platform.isAndroid) {
        downloadsDir = Directory('/storage/emulated/0/Download');
        if (!await downloadsDir.exists()) {
          final directory = await getExternalStorageDirectory();
          if (directory == null) {
            _logger.e('Could not access external storage');
            return false;
          }
          downloadsDir = directory;
        }
      } else {
        final directory = await getExternalStorageDirectory();
        if (directory == null) {
          _logger.e('Could not access external storage');
          return false;
        }
        downloadsDir = directory;
      }

      // Create backup directory
      Directory backupDir = Directory('${downloadsDir.path}/tubewell_backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // Generate backup filename
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupPath = '${backupDir.path}/tubewell_backup_$timestamp.db';

      // Store account name in the backup before copying
      await _storeAccountNameInBackup(backupFile.path);

      // Copy backup file
      await backupFile.copy(backupPath);
      await backupFile.delete();

      // Save backup information
      final now = DateTime.now();
      final formattedDate = DateFormat('MMM d, yyyy HH:mm').format(now);
      await SettingsService.saveLastBackupDate(formattedDate);

      _logger.i('Background backup completed successfully: $backupPath');
      return true;
    } catch (e) {
      _logger.e('Background backup failed: $e');
      return false;
    }
  }

  /// Create backup file
  static Future<File> _createBackupFile() async {
    final tempDir = await getTemporaryDirectory();
    final backupFile = File('${tempDir.path}/temp_backup.db');

    try {
      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      if (await backupFile.exists()) {
        await backupFile.delete();
      }
      await backupFile.create(recursive: true);

      // Get database path and copy it
      final dbPath = await DatabaseService.getDatabasePath();
      final dbFile = File(dbPath);

      if (await dbFile.exists()) {
        await dbFile.copy(backupFile.path);
        _logger.i('Database copied to temp location: ${backupFile.path}');
      } else {
        // Create minimal backup if database doesn't exist
        await backupFile.writeAsString('TUBEWELL_BACKUP\n${DateTime.now()}\nNo database found');
        _logger.w('Database file not found, created minimal backup');
      }

      return backupFile;
    } catch (e) {
      _logger.e('Error creating backup file: $e');
      // Create emergency backup
      await backupFile.writeAsString('Emergency backup created at ${DateTime.now()}');
      return backupFile;
    }
  }
}

/// Callback dispatcher for WorkManager
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    final logger = Logger();

    try {
      logger.i('Background task started: $task');

      switch (task) {
        case 'scheduled_backup_task':
          // Initialize necessary services for background operation
          await _initializeBackgroundServices();

          // Perform backup
          final success = await BackgroundBackupService.performBackup();

          logger.i('Background backup task completed: ${success ? 'SUCCESS' : 'FAILED'}');
          return success;

        default:
          logger.w('Unknown background task: $task');
          return false;
      }
    } catch (e) {
      logger.e('Background task error: $e');
      return false;
    }
  });
}

/// Initialize services needed for background operation
Future<void> _initializeBackgroundServices() async {
  try {
    // Initialize SharedPreferences
    await SharedPreferences.getInstance();

    // Initialize DatabaseService if needed
    if (!DatabaseService.isInitialized) {
      await DatabaseService.initialize();
    }
  } catch (e) {
    Logger().e('Failed to initialize background services: $e');
  }
}
*/

// Temporary placeholder class while WorkManager is disabled
import 'package:sqflite/sqflite.dart';
import 'package:logger/logger.dart';
import 'package:tubewell_water_billing/services/account_service.dart';

class BackgroundBackupService {
  static final Logger _logger = Logger();

  static Future<void> initialize() async {
    // Placeholder - WorkManager temporarily disabled
  }

  static Future<void> schedulePeriodicBackup({required int intervalHours}) async {
    // Placeholder - WorkManager temporarily disabled
  }

  static Future<void> cancelScheduledBackup() async {
    // Placeholder - WorkManager temporarily disabled
  }

  /// Store the current account name in the backup database
  static Future<void> _storeAccountNameInBackup(String backupPath) async {
    Database? backupDb;
    try {
      // Open the backup database
      backupDb = await openDatabase(backupPath);

      // Get current account information
      final currentAccount = AccountService.currentAccount;
      if (currentAccount != null) {
        // Store account name in settings table
        await backupDb.rawInsert(
          'INSERT OR REPLACE INTO settings (key, value, accountId) VALUES (?, ?, ?)',
          ['accountName', currentAccount.name, currentAccount.id]
        );
        _logger.i('Stored account name "${currentAccount.name}" in backup');
      }
    } catch (e) {
      _logger.e('Error storing account name in backup: $e');
      // Don't throw - this is not critical for the backup process
    } finally {
      if (backupDb != null) {
        try {
          await backupDb.close();
        } catch (e) {
          _logger.e('Error closing backup database: $e');
        }
      }
    }
  }
}
