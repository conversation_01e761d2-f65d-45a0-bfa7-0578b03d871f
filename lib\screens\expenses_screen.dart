import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/services/expense_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/app_drawer.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/forms/expense_form_screen.dart';
import 'package:tubewell_water_billing/widgets/universal_fab.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/utils/navigation_helper.dart';

import 'package:tubewell_water_billing/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';

// Sort options for expenses
enum ExpenseSortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  categoryAZ(label: 'Category (A-Z)', icon: Icons.arrow_downward),
  categoryZA(label: 'Category (Z-A)', icon: Icons.arrow_upward);

  final String label;
  final IconData icon;

  const ExpenseSortOption({
    required this.label,
    required this.icon,
  });
}

class ExpensesScreen extends StatefulWidget {
  const ExpensesScreen({super.key});

  @override
  State<ExpensesScreen> createState() => _ExpensesScreenState();
}

class _ExpensesScreenState extends State<ExpensesScreen> {
  // Constants
  static const int _pageSize = 20;

  // Pagination
  int _currentPage = 0;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // State variables
  List<Expense> _expenses = [];
  bool _isLoading = true;
  String _searchQuery = '';

  // Search state
  final TextEditingController _searchController = TextEditingController();

  // Sort state
  ExpenseSortOption _currentSortOption = ExpenseSortOption.dateNewest;
  bool _isSortActive = false;

  // Debounce search to reduce unnecessary rebuilds
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // Summary data
  Map<String, double> _expenseSummary = {
    'totalAmount': 0.0,
    'count': 0.0,
  };
  Map<String, double> _categoryTotals = {};

  // Scroll controller for pagination
  final ScrollController _scrollController = ScrollController();

  // Selection mode
  bool _isSelectionMode = false;
  final Set<int> _selectedExpenseIds = <int>{};

  // Category icons map
  final Map<String, IconData> _categoryIcons = {
    'Electricity': Icons.electric_bolt,
    'Maintenance': Icons.handyman,
    'Repairs': Icons.build,
    'Fuel': Icons.local_gas_station,
    'Salaries': Icons.payments,
    'Equipment': Icons.construction,
    'Rent': Icons.home,
    'Taxes': Icons.receipt_long,
    'Transportation': Icons.directions_car,
    'Office Supplies': Icons.business_center,
    'Other': Icons.category,
  };





  // Stream subscription for data changes
  late StreamSubscription<DataChangeType> _dataChangeSubscription;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(_onSearchChanged);
    _loadExpenses();

    // Listen for data changes
    _dataChangeSubscription =
        DataChangeNotifierService().onDataChanged.listen((changeType) {
      if (changeType == DataChangeType.expense ||
          changeType == DataChangeType.all) {
        if (mounted) {
          _refreshExpenses();
        }
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _dataChangeSubscription.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      final query = _searchController.text.trim().toLowerCase();
      if (_searchQuery != query) {
        setState(() {
          _searchQuery = query;
        });
        _loadExpenses(resetPagination: true);
      }
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >
        _scrollController.position.maxScrollExtent - 200) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMoreExpenses();
      }
    }
  }

  Future<void> _loadExpenses({bool resetPagination = true}) async {
    if (resetPagination) {
      setState(() {
        _isLoading = true;
        _currentPage = 0;
      });
    }

    try {
      // Load summary data
      final summary = await ExpenseService.getExpenseSummary();
      final categoryTotals = await ExpenseService.getExpensesByCategory();

      // Load expense list
      final expenses = await ExpenseService.getAllExpenses(
        offset: _currentPage * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      if (mounted) {
        setState(() {
          _expenseSummary = summary;
          _categoryTotals = categoryTotals;

          if (resetPagination) {
            _expenses = expenses;
          } else {
            _expenses.addAll(expenses);
          }

          // Apply sorting
          _sortExpenses(_expenses);

          _isLoading = false;
          _hasMoreData = expenses.length >= _pageSize;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading expenses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadMoreExpenses() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;

      final expenses = await ExpenseService.getAllExpenses(
        offset: _currentPage * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      if (mounted) {
        setState(() {
          _expenses.addAll(expenses);
          // Apply sorting to the complete list
          _sortExpenses(_expenses);
          _hasMoreData = expenses.length >= _pageSize;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading more expenses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshExpenses() async {
    return _loadExpenses(resetPagination: true);
  }

  // Sort expenses based on current sort option
  void _sortExpenses(List<Expense> expenses) {
    switch (_currentSortOption) {
      case ExpenseSortOption.dateNewest:
        expenses.sort((a, b) => b.date.compareTo(a.date));
        break;
      case ExpenseSortOption.dateOldest:
        expenses.sort((a, b) => a.date.compareTo(b.date));
        break;
      case ExpenseSortOption.amountHighest:
        expenses.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case ExpenseSortOption.amountLowest:
        expenses.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case ExpenseSortOption.categoryAZ:
        expenses.sort((a, b) {
          final comparison = a.category.compareTo(b.category);
          return comparison != 0 ? comparison : b.date.compareTo(a.date);
        });
        break;
      case ExpenseSortOption.categoryZA:
        expenses.sort((a, b) {
          final comparison = b.category.compareTo(a.category);
          return comparison != 0 ? comparison : b.date.compareTo(a.date);
        });
        break;
    }
  }

  // Handle sort option change
  void _onSortChanged(ExpenseSortOption option) {
    setState(() {
      _currentSortOption = option;
      _isSortActive = option != ExpenseSortOption.dateNewest;
    });

    // Re-sort current expenses
    _sortExpenses(_expenses);
    setState(() {});
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
    });
    _loadExpenses(resetPagination: true);
  }

  Future<void> _deleteExpense(Expense expense) async {
    try {
      final deleted = await ExpenseService.deleteExpense(expense.id);

      if (mounted) {
        if (deleted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Expense deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          _refreshExpenses();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete expense'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting expense: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmation(Expense expense) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Expense'),
        content: Text('Are you sure you want to delete this expense?\n\n'
            'Amount: ${CurrencyService.formatCurrency(expense.amount)}\n'
            'Category: ${expense.category}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteExpense(expense);
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToExpenseForm({Expense? existingExpense}) async {
    await NavigationHelper.navigateTo(
      context,
      ExpenseFormScreen(
        existingExpense: existingExpense,
      ),
    );

    if (mounted) {
      _refreshExpenses();
    }
  }

  Future<void> _generateExpensesPdf() async {
    if (!mounted) return;

    try {
      // Get all expenses that match the current search
      final allExpenses = await ExpenseService.getAllExpenses(
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        // Don't use pagination for PDF generation
        offset: 0,
        limit: 1000, // Use a large limit to get all expenses
      );

      // Apply current sorting to the expenses
      _sortExpenses(allExpenses);

      if (!mounted) return;

      // Create PDF data
      final Map<String, dynamic> pdfData = {
        'expenses': allExpenses,
        'summary': _expenseSummary,
        'categoryTotals': _categoryTotals,
        'searchQuery': _searchQuery.isNotEmpty ? _searchQuery : null,
        'sortOption': _currentSortOption.label,
      };

      // Use the universal PDF service to generate the PDF
      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modern().copyWith(
          additionalSettings: {
            'footerText': 'Expense Report',
          },
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Show sort options dialog
  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Expenses'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ExpenseSortOption.values.map((option) {
            final isSelected = option == _currentSortOption;
            return ListTile(
              leading: Icon(
                option.icon,
                color: isSelected ? Colors.purple : null,
              ),
              title: Text(option.label),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.purple)
                  : null,
              selected: isSelected,
              selectedColor: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _onSortChanged(option);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseItem(Expense expense) {
    final categoryIcon = _categoryIcons[expense.category] ?? Icons.category;
    final formattedDate = DateFormat('dd MMM yyyy').format(expense.date);
    final formattedAmount = CurrencyService.formatCurrency(expense.amount);
    final isSelected = _selectedExpenseIds.contains(expense.id);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected ? Colors.amber.shade500 : Colors.indigo.shade400,
            width: isSelected ? 2.0 : 1.5,
          ),
        ),
        color: isSelected ? Colors.amber.shade50 : null,
        child: InkWell(
          onTap: _isSelectionMode
              ? () {
                  setState(() {
                    if (_selectedExpenseIds.contains(expense.id)) {
                      _selectedExpenseIds.remove(expense.id);
                      if (_selectedExpenseIds.isEmpty) {
                        _isSelectionMode = false;
                      }
                    } else {
                      _selectedExpenseIds.add(expense.id);
                    }
                  });
                  HapticFeedback.selectionClick();
                }
              : () => _navigateToExpenseForm(existingExpense: expense),
          onLongPress: _isSelectionMode
              ? null
              : () {
                  setState(() {
                    _isSelectionMode = true;
                    _selectedExpenseIds.add(expense.id);
                  });
                  HapticFeedback.heavyImpact();
                },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Category and Payment Method
                Row(
                  children: [
                    // Selection checkbox when in selection mode
                    if (_isSelectionMode) ...[
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) {
                          setState(() {
                            if (isSelected) {
                              _selectedExpenseIds.remove(expense.id);
                              if (_selectedExpenseIds.isEmpty) {
                                _isSelectionMode = false;
                              }
                            } else {
                              _selectedExpenseIds.add(expense.id);
                            }
                          });
                          HapticFeedback.selectionClick();
                        },
                        activeColor: Colors.amber.shade700,
                        checkColor: Colors.white,
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Category with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.teal.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.teal.shade200),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              categoryIcon,
                              color: Colors.teal.shade700,
                              size: 14,
                            ),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                expense.category,
                                style: TextStyle(
                                  color: Colors.teal.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Payment Method with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(_getPaymentMethodIcon(expense.paymentMethod),
                                size: 14, color: Colors.blue.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                expense.paymentMethod ?? 'Cash',
                                style: TextStyle(
                                  color: Colors.blue.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button in row 1 (only when not in selection mode)
                    if (!_isSelectionMode)
                      GestureDetector(
                        onTap: () =>
                            _navigateToExpenseForm(existingExpense: expense),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.cyan.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.cyan.shade200),
                          ),
                          child: Icon(Icons.edit,
                              color: Colors.cyan.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Row 2: Date and Amount
                Row(
                  children: [
                    // Date with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 14, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Amount with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.payments,
                                size: 14, color: Colors.red.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedAmount,
                                style: TextStyle(
                                  color: Colors.red.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    if (!_isSelectionMode)
                      GestureDetector(
                        onTap: () => _showDeleteConfirmation(expense),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Icon(Icons.delete,
                              color: Colors.red.shade700, size: 12),
                        ),
                      ),
                  ],
                ),

                // Row 3: Remarks (if available)
                if (expense.remarks != null && expense.remarks!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade50,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: Colors.amber.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.comment,
                            size: 14, color: Colors.amber.shade700),
                        const SizedBox(width: 3),
                        Expanded(
                          child: Text(
                            expense.remarks!,
                            style: TextStyle(
                              color: Colors.amber.shade800,
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScrollableContent(Widget content) {
    if (_isLoading && _expenses.isEmpty) {
      // Show loading indicator when initially loading
      return content;
    } else if (_expenses.isEmpty) {
      // Show empty state when no expenses are found
      return content;
    } else {
      // Show scrollable content with summary and expense list
      return CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // Summary card as a sliver
          SliverToBoxAdapter(
            child: _buildExpenseSummaryCard(),
          ),

          // Expense list as a sliver
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index == _expenses.length) {
                  return _isLoadingMore
                      ? const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: CircularProgressIndicator(),
                          ),
                        )
                      : const SizedBox.shrink();
                }

                final expense = _expenses[index];
                return _buildExpenseItem(expense);
              },
              childCount: _expenses.length + (_hasMoreData ? 1 : 0),
            ),
          ),

          // Bottom padding for FAB
          const SliverToBoxAdapter(
            child: SizedBox(height: 80),
          ),
        ],
      );
    }
  }

  Widget _buildExpensesList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 80), // Space for FAB
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _expenses.length + (_hasMoreData ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _expenses.length) {
          return _isLoadingMore
              ? const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              : const SizedBox.shrink();
        }

        final expense = _expenses[index];
        return _buildExpenseItem(expense);
      },
    );
  }

  Widget _buildExpenseSummaryCard() {
    // Get top categories
    List<MapEntry<String, double>> topCategories = [];
    if (_categoryTotals.isNotEmpty) {
      topCategories = _categoryTotals.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      // Limit to top 3 categories
      if (topCategories.length > 3) {
        topCategories = topCategories.sublist(0, 3);
      }
    }

    // Ensure we have valid summary values
    final int expenseCount = _expenseSummary['count']?.toInt() ?? 0;
    final double totalAmount =
        _expenseSummary['totalAmount']?.toDouble() ?? 0.0;

    // Calculate average expense
    final double averageAmount =
        expenseCount > 0 ? totalAmount / expenseCount : 0.0;

    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.indigo.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.shade200),
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Expenses Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo.shade900,
                  fontSize: 18,
                ),
              ),
              Text(
                '$expenseCount expenses',
                style: TextStyle(
                  color: Colors.indigo.shade800,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Main amount with highlight
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.indigo.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.indigo.shade300),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.money_off,
                      color: Colors.indigo.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Total Expenses',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                        color: Colors.indigo.shade800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _isLoading
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          CurrencyService.formatCurrency(totalAmount),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                            color: Colors.indigo.shade700,
                          ),
                        ),
                      ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Detailed stats
          Row(
            children: [
              // Average expense
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: 'Average',
                  value: CurrencyService.formatCurrency(averageAmount),
                  icon: Icons.calculate,
                  iconColor: Colors.purple.shade700,
                  bgColor: Colors.purple.shade50,
                  borderColor: Colors.purple.shade200,
                  textColor: Colors.purple.shade800,
                ),
              ),
              const SizedBox(width: 16),
              // Top category if available
              Expanded(
                child: _buildFinancialSummaryItem(
                  title: topCategories.isNotEmpty
                      ? 'Top Category'
                      : 'No Categories',
                  value:
                      topCategories.isNotEmpty ? topCategories.first.key : '-',
                  icon: topCategories.isNotEmpty
                      ? _categoryIcons[topCategories.first.key] ??
                          Icons.category
                      : Icons.category,
                  iconColor: Colors.teal.shade700,
                  bgColor: Colors.teal.shade50,
                  borderColor: Colors.teal.shade200,
                  textColor: Colors.teal.shade800,
                ),
              ),
            ],
          ),


        ],
      ),
    );
  }

  // Helper method to build a financial summary item
  Widget _buildFinancialSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required Color bgColor,
    required Color borderColor,
    required Color textColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 16),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: textColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: textColor,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get payment method icon
  IconData _getPaymentMethodIcon(String? method) {
    if (method == null) return Icons.money;

    switch (method.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'bank transfer':
        return Icons.account_balance;
      case 'check':
        return Icons.receipt_long;
      case 'credit card':
        return Icons.credit_card;
      default:
        return Icons.payment;
    }
  }

  void _deleteSelectedExpenses() async {
    if (_selectedExpenseIds.isEmpty) return;

    final count = _selectedExpenseIds.length;

    // Confirm deletion
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Expenses'),
        content:
            Text('Are you sure you want to delete $count selected expenses?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    try {
      int successCount = 0;

      // Delete each selected expense
      for (final id in _selectedExpenseIds) {
        final success = await ExpenseService.deleteExpense(id);
        if (success) successCount++;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deleted $successCount of $count expenses'),
            backgroundColor: successCount > 0 ? Colors.green : Colors.orange,
          ),
        );

        // Exit selection mode and refresh
        setState(() {
          _isSelectionMode = false;
          _selectedExpenseIds.clear();
        });

        _refreshExpenses();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting expenses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    if (_isLoading && _expenses.isEmpty) {
      // Show loading indicator when initially loading
      content = const Center(child: CircularProgressIndicator());
    } else if (_expenses.isEmpty) {
      // Show empty state when no expenses are found
      content = EmptyStateWidget(
        icon: _searchQuery.isNotEmpty
            ? Icons.search_off
            : Icons.receipt_long,
        title: _searchQuery.isNotEmpty
            ? 'No Results Found'
            : 'No Expenses Yet',
        message: _searchQuery.isNotEmpty
            ? 'Try different search terms'
            : 'Add your first expense to get started.',
        buttonText: _searchQuery.isNotEmpty
            ? 'Clear Search'
            : 'Add Expense',
        onButtonPressed: () {
          if (_searchQuery.isNotEmpty) {
            _clearSearch();
          } else {
            _navigateToExpenseForm();
          }
        },
      );
    } else {
      // Show expense list
      content = _buildExpensesList();
    }

    return Scaffold(
      appBar: _isSelectionMode
          ? AppBar(
              backgroundColor: const Color(0xFF2E7D32),
              title: Text('${_selectedExpenseIds.length} selected'),
              leading: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = false;
                    _selectedExpenseIds.clear();
                  });
                },
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: _selectedExpenseIds.isEmpty
                      ? null
                      : () => _deleteSelectedExpenses(),
                ),
              ],
            )
          : TubewellAppBar(
              title: 'Expenses',
              currentScreen: 'expenses',
              showBackButton: false,
              onPdfPressed: _generateExpensesPdf,
              pdfData: {
                'expenses': _expenses,
                'summary': _expenseSummary,
                'categoryTotals': _categoryTotals,
                'searchQuery': _searchQuery.isNotEmpty ? _searchQuery : null,
                'sortOption': _currentSortOption.label,
              },
            ),
      drawer: const AppDrawer(currentScreen: 'expenses'),
      body: Column(
        children: [
          // Search and filter bar - keep fixed at top
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search expenses...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                  },
                                )
                              : null,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: Colors.indigo.shade300),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Sort button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: IconButton(
                        icon: Stack(
                          alignment: Alignment.center,
                          children: [
                            const Icon(Icons.sort),
                            if (_isSortActive)
                              Positioned(
                                right: 0,
                                bottom: 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.purple,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        tooltip: 'Sort: ${_currentSortOption.label}',
                        onPressed: _showSortDialog,
                      ),
                    ),
                  ],
                ),

                // Search results indicator
                if (_searchQuery.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        'Found ${_expenses.length} results',
                        style: TextStyle(
                          color: Colors.indigo.shade600,
                          fontStyle: FontStyle.italic,
                          fontSize: 13,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          _searchController.clear();
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: const Color(0xFF2E7D32),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          minimumSize: const Size(0, 32),
                        ),
                        child: const Text('Clear Search'),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Scrollable content area
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshExpenses,
              color: const Color(0xFF2E7D32),
              child: _buildScrollableContent(content),
            ),
          ),
        ],
      ),
      floatingActionButton: _isSelectionMode
          ? null
          : UniversalFab(
              type: FabType.expense,
              heroTag: 'fab-expenses',
              onResult: (result) {
                if (result == true) {
                  _refreshExpenses();
                }
              },
            ),
    );
  }
}
