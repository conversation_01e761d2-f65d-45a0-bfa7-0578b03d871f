import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';

/// A service layer for managing expense-related operations.
///
/// This service acts as an intermediary between the UI/business logic and
/// the `DatabaseService`, providing a clear API for expense management.
class ExpenseService {
  // Private constructor to prevent instantiation of this utility class.
  ExpenseService._();

  /// Predefined expense categories.
  static const List<String> predefinedCategories = [
    'Electricity',
    'Maintenance',
    'Repairs',
    'Fuel',
    'Salaries',
    'Equipment',
    'Rent',
    'Taxes',
    'Transportation',
    'Office Supplies',
    'Other',
  ];

  /// Get all expenses with filtering options
  ///
  /// [offset]: The starting index for pagination.
  /// [limit]: The maximum number of expenses to return.
  /// [searchQuery]: A string to search for in expense descriptions or notes.
  /// [category]: Filter expenses by a specific category.
  /// [startDate]: Filter expenses on or after this date.
  /// [endDate]: Filter expenses on or before this date.
  /// [paymentMethod]: Filter expenses by payment method.
  static Future<List<Expense>> getAllExpenses({
    int offset = 0,
    int limit = 20,
    String? searchQuery,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    String? paymentMethod,
  }) async {
    return DatabaseService.getAllExpenses(
      offset: offset,
      limit: limit,
      searchQuery: searchQuery,
      category: category,
      startDate: startDate,
      endDate: endDate,
      paymentMethod: paymentMethod,
    );
  }

  /// Get recent expenses
  ///
  /// Returns the most recent expenses, limited by the specified count.
  static Future<List<Expense>> getRecentExpenses({int limit = 5}) async {
    return DatabaseService.getRecentExpenses(limit: limit);
  }

  /// Get expense by ID
  ///
  /// [id]: The ID of the expense to retrieve.
  /// Returns a `Future` that completes with the `Expense` object, or `null` if not found.
  static Future<Expense?> getExpenseById(int id) async {
    return DatabaseService.getExpenseById(id);
  }

  /// Save a new expense
  ///
  /// [expense]: The `Expense` object to save.
  /// Returns a `Future` that completes with the ID of the newly created expense,
  /// or a value <= 0 on failure (depending on `DatabaseService` implementation).
  static Future<int> saveExpense(Expense expense) async {
    // OPTIMIZATION: Get the current time before database operation
    final startTime = DateTime.now();

    final expenseId = await DatabaseService.saveExpense(expense);

    // Notify listeners about the expense change only if successful
    if (expenseId > 0) {
      // OPTIMIZATION: Check if operation was fast enough for immediate update
      final operationTime = DateTime.now().difference(startTime);

      // Use a more specific notification for better real-time handling
      // Only use a delay if the operation took too long to avoid UI jank
      if (operationTime.inMilliseconds < 100) {
        // Fast operation - update UI immediately
        DataChangeNotifierService().notifyDataChanged(DataChangeType.expense);
      } else {
        // Slower operation - slight delay to allow UI to finish rendering
        Future.delayed(const Duration(milliseconds: 50), () {
          DataChangeNotifierService().notifyDataChanged(DataChangeType.expense);
        });
      }
    }

    return expenseId;
  }

  /// Update an existing expense
  ///
  /// [expense]: The `Expense` object with updated information. Its ID must be set.
  /// Returns a `Future` that completes with the number of rows affected (typically 1 on success, 0 if not found).
  static Future<int> updateExpense(Expense expense) async {
    final rowsAffected = await DatabaseService.updateExpense(expense);

    // Notify listeners about the expense change
    if (rowsAffected > 0) {
      DataChangeNotifierService().notifyDataChanged(DataChangeType.expense);
    }

    return rowsAffected;
  }

  /// Delete an expense
  ///
  /// [id]: The ID of the expense to delete.
  /// Returns a `Future` that completes with `true` if deletion was successful, `false` otherwise.
  static Future<bool> deleteExpense(int id) async {
    final success = await DatabaseService.deleteExpense(id);

    // Notify listeners about the expense change
    if (success) {
      DataChangeNotifierService().notifyDataChanged(DataChangeType.expense);
    }

    return success;
  }

  /// Get expense summary
  ///
  /// [startDate]: Optional start date for filtering the summary.
  /// [endDate]: Optional end date for filtering the summary.
  /// [category]: Optional category for filtering the summary.
  /// Returns a `Future` that completes with a map, e.g., `{'total': 1234.56}`.
  static Future<Map<String, double>> getExpenseSummary({
    DateTime? startDate,
    DateTime? endDate,
    String? category,
  }) async {
    // Get the base summary
    final summary = await DatabaseService.getExpenseSummary(
      startDate: startDate,
      endDate: endDate,
    );

    // If category filter is specified, we need to filter the results
    if (category != null && category.isNotEmpty) {
      // Get expenses by category
      final categoryExpenses = await DatabaseService.getAllExpenses(
        startDate: startDate,
        endDate: endDate,
        category: category,
      );

      // Calculate totals for this category
      final double totalAmount = categoryExpenses.fold(0.0, (sum, expense) => sum + expense.amount);
      final int count = categoryExpenses.length;

      // Update the summary with filtered data
      summary['totalAmount'] = totalAmount;
      summary['count'] = count.toDouble();

      // Calculate average if we have expenses
      if (count > 0) {
        summary['averageAmount'] = totalAmount / count;
      } else {
        summary['averageAmount'] = 0.0;
      }

      // Find min and max if we have expenses
      if (categoryExpenses.isNotEmpty) {
        categoryExpenses.sort((a, b) => a.amount.compareTo(b.amount));
        summary['minAmount'] = categoryExpenses.first.amount;
        summary['maxAmount'] = categoryExpenses.last.amount;
      } else {
        summary['minAmount'] = 0.0;
        summary['maxAmount'] = 0.0;
      }
    }

    return summary;
  }

  /// Get expenses grouped by category
  ///
  /// [startDate]: Optional start date for filtering.
  /// [endDate]: Optional end date for filtering.
  /// Returns a `Future` that completes with a map where keys are category names
  /// and values are the total expenses for that category.
  static Future<Map<String, double>> getExpensesByCategory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return DatabaseService.getExpensesByCategory(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get account summary (income, expenses, net balance)
  ///
  /// [startDate]: Optional start date for filtering.
  /// [endDate]: Optional end date for filtering.
  /// Returns a `Future` that completes with a map containing financial summary figures.
  /// Example: `{'income': 5000.0, 'expenses': 1500.0, 'netBalance': 3500.0}`.
  static Future<Map<String, double>> getAccountSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return DatabaseService.getAccountSummary(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get predefined expense categories
  ///
  /// Returns a list of predefined expense categories.
  static List<String> getPredefinedCategories() {
    return predefinedCategories;
  }
}
