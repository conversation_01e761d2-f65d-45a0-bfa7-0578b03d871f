class Customer {
  int id;
  String name;
  String? contactNumber;
  DateTime createdAt;
  double balance;

  Customer({
    this.id = 0,
    required this.name,
    this.contactNumber,
    DateTime? createdAt,
    this.balance = 0.0,
  }) : createdAt = createdAt ?? DateTime.now();

  // Convert a Customer object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id == 0 ? null : id, // SQLite will auto-assign if null
      'name': name,
      'contactNumber': contactNumber,
      'createdAt': createdAt.toIso8601String(),
      'balance': balance,
    };
  }

  // Create a Customer object from a Map
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id'],
      name: map['name'],
      contactNumber: map['contactNumber'],
      createdAt: DateTime.parse(map['createdAt']),
      balance: map['balance'] != null ? map['balance'].toDouble() : 0.0,
    );
  }
}
