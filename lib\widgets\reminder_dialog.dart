import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/message_service.dart';

/// A dialog that shows options to send reminders for unpaid bills
class ReminderDialog extends StatefulWidget {
  final Customer customer;
  final List<Bill> bills;
  final String title;

  const ReminderDialog({
    super.key,
    required this.customer,
    required this.bills,
    this.title = 'Send Payment Reminder',
  });

  @override
  State<ReminderDialog> createState() => _ReminderDialogState();
}

class _ReminderDialogState extends State<ReminderDialog> {
  String _message = 'Loading message...';
  bool _isSending = false;
  bool _isLoading = true;
  String? _errorMessage;
  final TextEditingController _customMessageController =
      TextEditingController();
  bool _useCustomMessage = false;

  @override
  void initState() {
    super.initState();
    _initializeMessage();
  }

  Future<void> _initializeMessage() async {
    try {
      _message = await MessageService.formatReminderMessage(
          widget.bills, widget.customer);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _message = 'Error loading message: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateMessage() async {
    setState(() {
      _isLoading = true;
    });

    try {
      String newMessage;
      if (_useCustomMessage && _customMessageController.text.isNotEmpty) {
        newMessage = await MessageService.formatReminderMessage(
          widget.bills,
          widget.customer,
          customMessage: _customMessageController.text,
        );
      } else {
        newMessage = await MessageService.formatReminderMessage(
            widget.bills, widget.customer);
      }

      if (mounted) {
        setState(() {
          _message = newMessage;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error updating message: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sendWhatsApp() async {
    if (widget.customer.contactNumber == null ||
        widget.customer.contactNumber!.isEmpty) {
      setState(() {
        _errorMessage = 'Customer does not have a contact number.';
      });
      return;
    }

    setState(() {
      _isSending = true;
      _errorMessage = null;
    });

    try {
      final success = await MessageService.sendWhatsAppMessage(
        widget.customer.contactNumber!,
        _message,
      );

      if (!success && mounted) {
        setState(() {
          _errorMessage =
              'Failed to open WhatsApp. Make sure WhatsApp is installed.';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  Future<void> _sendSms() async {
    if (widget.customer.contactNumber == null ||
        widget.customer.contactNumber!.isEmpty) {
      setState(() {
        _errorMessage = 'Customer does not have a contact number.';
      });
      return;
    }

    setState(() {
      _isSending = true;
      _errorMessage = null;
    });

    try {
      final success = await MessageService.sendSmsMessage(
        widget.customer.contactNumber!,
        _message,
      );

      if (!success && mounted) {
        setState(() {
          _errorMessage = 'Failed to open SMS app.';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _customMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.customer.contactNumber != null &&
                widget.customer.contactNumber!.isNotEmpty)
              Text(
                'Contact: ${widget.customer.contactNumber}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            const SizedBox(height: 16),

            // Custom message option
            SwitchListTile(
              title: const Text('Add Custom Message'),
              value: _useCustomMessage,
              onChanged: (value) {
                setState(() {
                  _useCustomMessage = value;
                  _updateMessage();
                });
              },
              activeColor: Colors.blue,
            ),

            if (_useCustomMessage) ...[
              const SizedBox(height: 8),
              TextField(
                controller: _customMessageController,
                decoration: const InputDecoration(
                  labelText: 'Custom Message',
                  hintText: 'Enter your custom reminder message',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                onChanged: (_) {
                  _updateMessage();
                },
              ),
              const SizedBox(height: 16),
            ],

            const Text('Message Preview:'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: _isLoading
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : Text(_message),
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        if (widget.customer.contactNumber != null &&
            widget.customer.contactNumber!.isNotEmpty) ...[
          ElevatedButton.icon(
            onPressed: _isSending ? null : _sendSms,
            icon: const Icon(Icons.sms),
            label: const Text('SMS'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: _isSending ? null : _sendWhatsApp,
            icon: const Icon(Icons.message),
            label: const Text('WhatsApp'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF25D366), // WhatsApp green
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ],
    );
  }
}
