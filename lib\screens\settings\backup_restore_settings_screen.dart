import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/widgets/bottom_navigation.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/settings_service.dart';
import 'package:tubewell_water_billing/services/permission_service.dart';
import 'package:tubewell_water_billing/services/file_service.dart';
import 'package:tubewell_water_billing/services/backup_service.dart';
import 'package:tubewell_water_billing/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart';
import 'package:tubewell_water_billing/widgets/backup_dialogs.dart';

import 'package:tubewell_water_billing/widgets/permission_error_dialog.dart';
import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'dart:io';

class BackupRestoreSettingsScreen extends StatefulWidget {
  const BackupRestoreSettingsScreen({super.key});

  @override
  State<BackupRestoreSettingsScreen> createState() =>
      _BackupRestoreSettingsScreenState();
}

class _BackupRestoreSettingsScreenState
    extends State<BackupRestoreSettingsScreen> {
  // State variables
  bool _isLoading = true;
  final bool _isBackingUp = false;
  bool _isRestoring = false;
  String _lastBackupDate = 'Never';

  // Backup service
  BackupService? _backupService;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize backup service
      _backupService = BackupService();

      // Load backup settings
      await _loadBackupSettings();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error initializing services: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadBackupSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize settings service
      await SettingsService.initialize();

      // Load last backup date
      final accountId = DatabaseService.getCurrentAccountId();
      final lastBackup =
          await SettingsService.getLastBackupDate(accountId: accountId);

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _lastBackupDate = lastBackup;
        _isLoading = false;
      });
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading backup settings: $e'),
          backgroundColor: Colors.red,
        ),
      );

      setState(() {
        _isLoading = false;
      });
    }
  }

  // Perform the actual backup operation
  Future<void> _performBackup() async {
    if (_backupService == null || !mounted) return;

    // Pass the context to the backupToLocalDevice method
    final result = await _backupService!.backupToLocalDevice(context);

    if (!mounted) return;

    if (result['success']) {
      setState(() {
        _lastBackupDate = DateFormat('MMM d, yyyy HH:mm').format(DateTime.now());
      });

      final snackBar = SnackBar(
        content: Text('Backup saved to: ${result['path']}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'VIEW',
          textColor: Colors.white,
          onPressed: () async {
            // Try to open the file location
            final success = await FileService.openFileLocation(result['path']);

            if (!success && mounted) {
              // If that fails, try to open the Downloads folder directly
              final downloadsPath = '/storage/emulated/0/Download';
              final downloadsSuccess = await FileService.openFileLocation(downloadsPath);

              if (!downloadsSuccess && mounted) {
                // If that also fails, show a more helpful error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Backup saved to: ${result['path']}\nCould not open file location automatically.'),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 8),
                  ),
                );
              }
            }
          },
        ),
      );

      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    } else {
      // Check if the error is related to permissions
      if (result['error'].toString().contains('permission')) {
        // Show our custom permission dialog for permission-related errors
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required to save backups to your device.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Try backup again after permission is granted
              _performBackup();
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );
      } else {
        // For other errors, show a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Backup failed: ${result['error'] ?? 'Please check storage permissions and try again.'}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _restoreBackup() async {
    setState(() {
      _isRestoring = true;
    });

    debugPrint('=== RESTORE BACKUP STARTED ===');
    final stopwatch = Stopwatch()..start();

    try {
      // Check storage permission status first without showing system dialog
      final permissionStatus = await PermissionService.checkStoragePermissionStatus();

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      // If permission is not granted, show our custom dialog
      if (!permissionStatus) {
        setState(() {
          _isRestoring = false;
        });

        // Show our custom permission dialog
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required to restore backups from your device.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Restart the restore process after permission is granted
              _restoreBackup();
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );

        return;
      }

      // First, try to open the Downloads folder to help the user find the backup
      if (Platform.isAndroid) {
        // Show a message to the user about where to look for backups
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a backup file from the Downloads/tubewell_backups folder'),
            duration: Duration(seconds: 3),
          ),
        );

        // Give the snackbar time to show before opening file picker
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Pick backup file
      FilePickerResult? result;
      try {
        // Try to set initial directory to Downloads folder on Android
        String? initialDirectory;
        if (Platform.isAndroid) {
          // Standard Android Downloads directory
          initialDirectory = '/storage/emulated/0/Download';

          // Check if tubewell_backups folder exists in Downloads
          final backupsDir = Directory('$initialDirectory/tubewell_backups');
          if (!await backupsDir.exists()) {
            // If not, just use Downloads folder
            initialDirectory = '/storage/emulated/0/Download';
          } else {
            // If it exists, use the backups folder
            initialDirectory = backupsDir.path;
          }
        }

        result = await FilePicker.platform.pickFiles(
          type: FileType.any, // Use any file type instead of custom with db extension
          initialDirectory: initialDirectory,
        );
      } catch (e) {
        // Try again with more basic options
        result = await FilePicker.platform.pickFiles();
      }

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      if (result == null || result.files.single.path == null) {
        setState(() {
          _isRestoring = false;
        });
        return;
      }

      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Confirm Restore'),
          content: const Text(
              'Restoring will replace all current data with the backup data. '
              'This action cannot be undone. Do you want to continue?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Restore'),
            ),
          ],
        ),
      );

      // Check if widget is still mounted before continuing
      if (!mounted) return;

      if (confirmed != true) {
        setState(() {
          _isRestoring = false;
        });
        return;
      }

      final backupFilePath = result.files.single.path!;

      // Check if backup file exists and is valid
      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        throw Exception('Backup file not found');
      }

      final backupFileSize = await backupFile.length();

      if (backupFileSize <= 0) {
        throw Exception('Backup file is empty or corrupted');
      }

      // Ensure database is completely closed with multiple attempts
      await _ensureDatabaseClosed();

      // STEP 1: Determine the authoritative account ID from the backup file itself
      debugPrint('=== STEP 1: DETERMINING AUTHORITATIVE ACCOUNT ID FROM BACKUP FILE ===');
      String authoritativeAccountId = await _determineAuthoritativeAccountId(backupFile.path);
      debugPrint('Authoritative account ID determined from backup: $authoritativeAccountId');

      // STEP 2: Determine the final destination path for this account's database
      debugPrint('=== STEP 2: DETERMINING FINAL DESTINATION PATH ===');
      final String finalActiveDbPath = await DatabaseService.getDatabasePathForAccount(authoritativeAccountId);
      debugPrint('Final active DB path for new account: $finalActiveDbPath');

      // Delete any existing file at the final destination path
      final existingFinalDbFile = File(finalActiveDbPath);
      if (await existingFinalDbFile.exists()) {
        try {
          await existingFinalDbFile.delete();
          debugPrint('Deleted existing database file at final destination: $finalActiveDbPath');
        } catch (e) {
          debugPrint('Warning: Could not delete existing database file at final destination: $e');
        }
      }
      await Future.delayed(const Duration(milliseconds: 200));

      // STEP 3: Copy backup directly to the final account-specific path
      debugPrint('=== STEP 3: COPYING BACKUP TO FINAL ACCOUNT-SPECIFIC PATH ===');
      await _copyBackupFileWithRetry(backupFile, finalActiveDbPath);
      debugPrint('Backup file copied to final destination: $finalActiveDbPath');

      // Verify the database file was created at the correct location
      final newDbFile = File(finalActiveDbPath);
      if (!await newDbFile.exists()) {
        throw Exception('Failed to create database file at $finalActiveDbPath after copy');
      }

      // Verify the copied file has the expected size
      final copiedFileSize = await newDbFile.length();
      if (copiedFileSize != backupFileSize) {
        throw Exception('Copied database file size mismatch. Expected: $backupFileSize, Got: $copiedFileSize');
      }

      debugPrint('Database file copied successfully. Size: $copiedFileSize bytes');

      // Wait a moment to ensure the file system has settled
      await Future.delayed(const Duration(milliseconds: 500));

      // STEP 4: Retrieve account name from backup and set as authoritative account
      debugPrint('=== STEP 4: RETRIEVING ACCOUNT NAME AND SETTING AUTHORITATIVE ACCOUNT ===');
      String accountName = 'Restored Account'; // Default fallback

      try {
        // Add timeout to prevent hanging
        accountName = await _getAccountNameFromBackup(finalActiveDbPath, authoritativeAccountId)
            .timeout(const Duration(seconds: 10));
        debugPrint('Retrieved account name from backup: $accountName');
      } catch (e) {
        debugPrint('Failed to retrieve account name from backup: $e, using default');
        accountName = 'Restored Account';
      }

      await AccountService.setActiveAccountAndClearOthers(authoritativeAccountId, accountName)
          .timeout(const Duration(seconds: 10));
      debugPrint('Authoritative account set successfully with name: $accountName');

      // Force AccountService to reinitialize to ensure proper state
      await AccountService.forceReinitialize()
          .timeout(const Duration(seconds: 10));
      debugPrint('AccountService reinitialized');

      // STEP 5: Re-initialize DatabaseService with the authoritative account ID
      debugPrint('=== STEP 5: RE-INITIALIZING DATABASE SERVICE ===');
      await DatabaseService.closeDatabase()
          .timeout(const Duration(seconds: 5));
      await Future.delayed(const Duration(milliseconds: 100));
      await DatabaseService.initialize(accountId: authoritativeAccountId)
          .timeout(const Duration(seconds: 15));
      debugPrint('DatabaseService re-initialized with authoritative account');

      // Store account name in settings now that database is initialized
      await AccountService.storeCurrentAccountNameInSettings()
          .timeout(const Duration(seconds: 5));
      debugPrint('Account name stored in settings for future backups');

      // STEP 6: Update all data in restored database to use authoritative account ID (if needed)
      debugPrint('=== STEP 6: ENSURING DATA CONSISTENCY ===');
      await _ensureDataConsistency(authoritativeAccountId)
          .timeout(const Duration(seconds: 20));
      debugPrint('Data consistency ensured');

      // STEP 7: Force global UI refresh
      debugPrint('=== STEP 7: TRIGGERING GLOBAL UI REFRESH ===');
      await _triggerGlobalUIRefresh()
          .timeout(const Duration(seconds: 10));
      debugPrint('Global UI refresh triggered');

      stopwatch.stop();
      debugPrint('=== RESTORE BACKUP COMPLETED SUCCESSFULLY ===');
      debugPrint('Total restore time: ${stopwatch.elapsedMilliseconds}ms');
      debugPrint('Authoritative account ID: $authoritativeAccountId');

      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      // Show success message and navigate to main screen
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Restore Successful'),
          content: const Text(
            'Database restored successfully. The app will now refresh to show the restored data.',
          ),
          actions: [
            TextButton(
              onPressed: () async {
                Navigator.pop(context); // Close dialog

                // Navigate back to main screen to force refresh
                debugPrint('Navigating to main screen to force refresh...');
                if (mounted) {
                  // Create BottomNavigation with forceRefresh to recreate all screens
                  final bottomNavigation = BottomNavigation(
                    initialIndex: 0,
                    forceRefresh: true,
                  );
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => bottomNavigation),
                    (route) => false,
                  );
                }
              },
              child: const Text('OK'),
            ),
          ],
        ),
      );
    } catch (e) {
      stopwatch.stop();
      debugPrint('=== RESTORE BACKUP FAILED ===');
      debugPrint('Error: $e');
      debugPrint('Time before failure: ${stopwatch.elapsedMilliseconds}ms');
      debugPrint('Stack trace: ${StackTrace.current}');

      // Try to reopen the database to prevent app from crashing
      try {
        // Just use the current account ID for recovery
        final accountId = DatabaseService.getCurrentAccountId();
        final dbService = DatabaseService();
        await dbService.initializeDatabase(accountId: accountId);
        debugPrint('Database recovery successful');
      } catch (reopenError) {
        debugPrint('Database recovery failed: $reopenError');
        // Ignore reopen errors
      }

      // Check if widget is still mounted before showing error
      if (!mounted) return;

      // Check if the error is related to permissions
      if (e.toString().contains('permission')) {
        // Show our custom permission dialog for permission-related errors
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PermissionErrorDialog(
            title: 'Storage Permission Required',
            message: 'Storage permission is required to restore backups from your device.',
            permissionType: 'storage',
            onPermissionGranted: () {
              // Restart the restore process after permission is granted
              _restoreBackup();
            },
            onCancel: () {
              // Do nothing, dialog will be dismissed
            },
          ),
        );
      } else {
        // For other errors, show a snackbar with retry option
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error restoring backup: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'RETRY',
              textColor: Colors.white,
              onPressed: () {
                _restoreBackup();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRestoring = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Backup & Restore',
        showPdfOption: false,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                _buildBackupInfo(),
                const SizedBox(height: 20),
                _buildManualBackupOptions(),
                const SizedBox(height: 20),
                _buildScheduledBackupOptions(),
                const SizedBox(height: 20),
                _buildRestoreOptions(),
              ],
            ),
    );
  }

  Widget _buildBackupInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Backup Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Regular backups help protect your data from loss. You can create manual backups or set up scheduled backups.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text('Last backup: $_lastBackupDate'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManualBackupOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Manual Backup',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Create a backup manually to your device.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isBackingUp
                      ? null
                      : () async {
                          if (_backupService != null) {
                            // Check permission status first
                            final permissionStatus = await PermissionService.checkStoragePermissionStatus();

                            if (!mounted) return;

                            // If permission is not granted, show our custom dialog
                            if (!permissionStatus) {
                              // Show our custom permission dialog
                              await showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) => PermissionErrorDialog(
                                  title: 'Storage Permission Required',
                                  message: 'Storage permission is required to save backups to your device.',
                                  permissionType: 'storage',
                                  onPermissionGranted: () async {
                                    // Try backup again after permission is granted
                                    if (_backupService != null && mounted) {
                                      _performBackup();
                                    }
                                  },
                                  onCancel: () {
                                    // Do nothing, dialog will be dismissed
                                  },
                                ),
                              );
                              return;
                            }

                            // Perform the backup
                            _performBackup();
                          }
                        },
                  icon: const Icon(Icons.phone_android),
                  label: const Text('Backup to Device'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduledBackupOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scheduled Backup',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Set up automatic backups at regular intervals.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            if (_backupService != null)
              ElevatedButton.icon(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => ScheduledBackupDialog(
                      backupService: _backupService!,
                    ),
                  );
                },
                icon: const Icon(Icons.schedule),
                label: const Text('Configure Scheduled Backup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRestoreOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Restore Backup',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Restore your data from a previous backup file.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isRestoring ? null : _restoreBackup,
              icon: _isRestoring
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.restore, color: Colors.white),
              label: const Text('Restore from Backup'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to ensure database is completely closed
  Future<void> _ensureDatabaseClosed() async {
    debugPrint('Ensuring database is completely closed...');

    // Close database multiple times with delays to ensure it's really closed
    for (int attempt = 1; attempt <= 3; attempt++) {
      try {
        await DatabaseService.closeDatabase();
        debugPrint('Database close attempt $attempt completed');

        // Wait between attempts to allow file system to settle
        if (attempt < 3) {
          await Future.delayed(Duration(milliseconds: 300 * attempt));
        }
      } catch (e) {
        debugPrint('Database close attempt $attempt failed: $e');
        // Continue with next attempt
      }
    }

    // Final wait to ensure all file handles are released
    await Future.delayed(const Duration(milliseconds: 1000));
    debugPrint('Database closure process completed');
  }

  // Helper method to copy backup file with retry logic
  Future<void> _copyBackupFileWithRetry(File backupFile, String dbPath) async {
    const maxRetries = 3;
    Exception? lastException;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('Attempting to copy backup file (attempt $attempt/$maxRetries)');

        // Use different copy strategies for better reliability
        if (attempt == 1) {
          // First attempt: Standard copy
          await backupFile.copy(dbPath);
        } else if (attempt == 2) {
          // Second attempt: Copy via bytes (more reliable on some systems)
          final bytes = await backupFile.readAsBytes();
          await File(dbPath).writeAsBytes(bytes, flush: true);
        } else {
          // Third attempt: Copy with explicit sync
          final bytes = await backupFile.readAsBytes();
          final targetFile = File(dbPath);
          await targetFile.writeAsBytes(bytes, flush: true);
          // Force file system sync
          await Future.delayed(const Duration(milliseconds: 500));
        }

        // Verify the copy was successful
        final targetFile = File(dbPath);
        if (await targetFile.exists()) {
          final targetSize = await targetFile.length();
          final sourceSize = await backupFile.length();

          if (targetSize == sourceSize && targetSize > 0) {
            debugPrint('Backup file copied successfully on attempt $attempt');
            return; // Success!
          } else {
            throw Exception('File size mismatch after copy. Source: $sourceSize, Target: $targetSize');
          }
        } else {
          throw Exception('Target file does not exist after copy');
        }

      } catch (e) {
        lastException = Exception('Copy attempt $attempt failed: $e');
        debugPrint('Copy attempt $attempt failed: $e');

        if (attempt < maxRetries) {
          // Wait before retry, with increasing delay
          await Future.delayed(Duration(milliseconds: 500 * attempt));

          // Clean up any partial file before retry
          try {
            final targetFile = File(dbPath);
            if (await targetFile.exists()) {
              await targetFile.delete();
              debugPrint('Cleaned up partial file before retry');
            }
          } catch (cleanupError) {
            debugPrint('Could not clean up partial file: $cleanupError');
          }
        }
      }
    }

    // If we get here, all attempts failed
    throw lastException ?? Exception('Failed to copy backup file after $maxRetries attempts');
  }

  // Method to update restored data with the current account ID
  // This is used for older backups that don't have account IDs
  Future<void> _updateRestoredDataWithAccountId(String accountId) async {
    try {
      final db = await DatabaseService.database;

      debugPrint('Updating restored data with account ID: $accountId');

      // First, let's check what data actually exists
      await _debugDatabaseContents(db);

      // Update customers table - Update ALL records to use the current account ID
      try {
        final customerUpdateCount = await db.rawUpdate(
          'UPDATE customers SET accountId = ?',
          [accountId]
        );
        debugPrint('Updated $customerUpdateCount customers with account ID');
      } catch (e) {
        debugPrint('Error updating customers table: $e');
      }

      // Update bills table - Update ALL records to use the current account ID
      try {
        final billUpdateCount = await db.rawUpdate(
          'UPDATE bills SET accountId = ?',
          [accountId]
        );
        debugPrint('Updated $billUpdateCount bills with account ID');
      } catch (e) {
        debugPrint('Error updating bills table: $e');
      }

      // Update payments table - Update ALL records to use the current account ID
      try {
        final paymentUpdateCount = await db.rawUpdate(
          'UPDATE payments SET accountId = ?',
          [accountId]
        );
        debugPrint('Updated $paymentUpdateCount payments with account ID');
      } catch (e) {
        debugPrint('Error updating payments table: $e');
      }

      // Update expenses table (if it exists) - Update ALL records to use the current account ID
      try {
        final tablesResult = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'"
        );
        if (tablesResult.isNotEmpty) {
          final expenseUpdateCount = await db.rawUpdate(
            'UPDATE expenses SET accountId = ?',
            [accountId]
          );
          debugPrint('Updated $expenseUpdateCount expenses with account ID');
        }
      } catch (e) {
        debugPrint('Error updating expenses table: $e');
      }

      // Update payment_allocations table (if it exists) - Update ALL records to use the current account ID
      try {
        final tablesResult = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='payment_allocations'"
        );
        if (tablesResult.isNotEmpty) {
          final allocationUpdateCount = await db.rawUpdate(
            'UPDATE payment_allocations SET accountId = ?',
            [accountId]
          );
          debugPrint('Updated $allocationUpdateCount payment allocations with account ID');
        }
      } catch (e) {
        debugPrint('Error updating payment_allocations table: $e');
      }

      // Update settings table (if it exists) - Update ALL records to use the current account ID
      try {
        final tablesResult = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='settings'"
        );
        if (tablesResult.isNotEmpty) {
          final settingsUpdateCount = await db.rawUpdate(
            'UPDATE settings SET accountId = ?',
            [accountId]
          );
          debugPrint('Updated $settingsUpdateCount settings with account ID');
        }
      } catch (e) {
        debugPrint('Error updating settings table: $e');
      }

      debugPrint('Finished updating restored data with account ID');
    } catch (e) {
      debugPrint('Error updating restored data with account ID: $e');
      // Don't throw - this is not critical for the restore process
    }
  }

  // Method to determine the authoritative account ID from database file
  Future<String> _determineAuthoritativeAccountId(String dbPathToInspect) async {
    Database? tempDb;
    try {
      // Open the database directly without using DatabaseService
      debugPrint('Opening database at $dbPathToInspect to inspect account IDs...');
      tempDb = await openDatabase(dbPathToInspect, readOnly: true);

      // Check for account IDs in data tables (customers, bills, payments, expenses)
      final accountIds = <String>{};

      // Check customers table
      try {
        final customers = await tempDb.rawQuery(
          'SELECT DISTINCT accountId FROM customers WHERE accountId IS NOT NULL AND accountId != ""'
        );
        for (final row in customers) {
          final accountId = row['accountId'] as String?;
          if (accountId != null && accountId.isNotEmpty) {
            accountIds.add(accountId);
          }
        }
      } catch (e) {
        debugPrint('Error checking customers table: $e');
      }

      // Check bills table
      try {
        final bills = await tempDb.rawQuery(
          'SELECT DISTINCT accountId FROM bills WHERE accountId IS NOT NULL AND accountId != ""'
        );
        for (final row in bills) {
          final accountId = row['accountId'] as String?;
          if (accountId != null && accountId.isNotEmpty) {
            accountIds.add(accountId);
          }
        }
      } catch (e) {
        debugPrint('Error checking bills table: $e');
      }

      // Check payments table
      try {
        final payments = await tempDb.rawQuery(
          'SELECT DISTINCT accountId FROM payments WHERE accountId IS NOT NULL AND accountId != ""'
        );
        for (final row in payments) {
          final accountId = row['accountId'] as String?;
          if (accountId != null && accountId.isNotEmpty) {
            accountIds.add(accountId);
          }
        }
      } catch (e) {
        debugPrint('Error checking payments table: $e');
      }

      // Check expenses table if it exists
      try {
        final tablesResult = await tempDb.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'"
        );
        if (tablesResult.isNotEmpty) {
          final expenses = await tempDb.rawQuery(
            'SELECT DISTINCT accountId FROM expenses WHERE accountId IS NOT NULL AND accountId != ""'
          );
          for (final row in expenses) {
            final accountId = row['accountId'] as String?;
            if (accountId != null && accountId.isNotEmpty) {
              accountIds.add(accountId);
            }
          }
        }
      } catch (e) {
        debugPrint('Error checking expenses table: $e');
      }

      debugPrint('Found account IDs in restored data: ${accountIds.toList()}');

      // If we found account IDs, use the first one as authoritative
      if (accountIds.isNotEmpty) {
        return accountIds.first;
      }

      // If no account IDs found, this is likely an old backup
      // Generate a new account ID and we'll update all records later
      final newAccountId = const Uuid().v4();
      debugPrint('No account IDs found in restored data, generated new ID: $newAccountId');
      return newAccountId;

    } catch (e) {
      debugPrint('Error determining authoritative account ID: $e');
      // Fallback: generate a new account ID
      final fallbackAccountId = const Uuid().v4();
      debugPrint('Using fallback account ID: $fallbackAccountId');
      return fallbackAccountId;
    } finally {
      // Always close the temporary database
      if (tempDb != null) {
        try {
          await tempDb.close();
          debugPrint('Temporary database closed');
        } catch (e) {
          debugPrint('Error closing temporary database: $e');
        }
      }
    }
  }

  // Method to ensure data consistency in restored database
  Future<void> _ensureDataConsistency(String authoritativeAccountId) async {
    try {
      final db = await DatabaseService.database;

      // Update all records to use the authoritative account ID
      // This is needed for old backups that might not have account IDs

      // Update customers table
      try {
        final customerUpdateCount = await db.rawUpdate(
          'UPDATE customers SET accountId = ? WHERE accountId IS NULL OR accountId = ""',
          [authoritativeAccountId]
        );
        debugPrint('Updated $customerUpdateCount customers with authoritative account ID');
      } catch (e) {
        debugPrint('Error updating customers table: $e');
      }

      // Update bills table
      try {
        final billUpdateCount = await db.rawUpdate(
          'UPDATE bills SET accountId = ? WHERE accountId IS NULL OR accountId = ""',
          [authoritativeAccountId]
        );
        debugPrint('Updated $billUpdateCount bills with authoritative account ID');
      } catch (e) {
        debugPrint('Error updating bills table: $e');
      }

      // Update payments table
      try {
        final paymentUpdateCount = await db.rawUpdate(
          'UPDATE payments SET accountId = ? WHERE accountId IS NULL OR accountId = ""',
          [authoritativeAccountId]
        );
        debugPrint('Updated $paymentUpdateCount payments with authoritative account ID');
      } catch (e) {
        debugPrint('Error updating payments table: $e');
      }

      // Update expenses table if it exists
      try {
        final tablesResult = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'"
        );
        if (tablesResult.isNotEmpty) {
          final expenseUpdateCount = await db.rawUpdate(
            'UPDATE expenses SET accountId = ? WHERE accountId IS NULL OR accountId = ""',
            [authoritativeAccountId]
          );
          debugPrint('Updated $expenseUpdateCount expenses with authoritative account ID');
        }
      } catch (e) {
        debugPrint('Error updating expenses table: $e');
      }

      // Update settings table
      try {
        await db.rawUpdate(
          'UPDATE settings SET accountId = ? WHERE accountId IS NULL OR accountId = ""',
          [authoritativeAccountId]
        );

        // Also ensure the currentAccountId setting is correct
        await db.rawInsert(
          'INSERT OR REPLACE INTO settings (key, value, accountId) VALUES (?, ?, ?)',
          ['currentAccountId', authoritativeAccountId, authoritativeAccountId]
        );
        debugPrint('Updated settings with authoritative account ID');
      } catch (e) {
        debugPrint('Error updating settings table: $e');
      }

      debugPrint('Data consistency ensured for account: $authoritativeAccountId');
    } catch (e) {
      debugPrint('Error ensuring data consistency: $e');
      // Don't throw - this is not critical for the restore process
    }
  }

  // Method to trigger global UI refresh
  Future<void> _triggerGlobalUIRefresh() async {
    try {
      debugPrint('Triggering global UI refresh...');

      // First, ensure all database operations are complete
      await Future.delayed(const Duration(milliseconds: 200));

      // Notify all screens to refresh their data multiple times to ensure it's received
      DataChangeNotifierService().notifyDataChanged(DataChangeType.all);
      await Future.delayed(const Duration(milliseconds: 50));
      DataChangeNotifierService().notifyDataChanged(DataChangeType.customer);
      await Future.delayed(const Duration(milliseconds: 50));
      DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
      await Future.delayed(const Duration(milliseconds: 50));
      DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);
      await Future.delayed(const Duration(milliseconds: 50));
      DataChangeNotifierService().notifyDataChanged(DataChangeType.expense);

      // Final delay to ensure all notifications are processed
      await Future.delayed(const Duration(milliseconds: 200));

      debugPrint('Global UI refresh completed');
    } catch (e) {
      debugPrint('Error triggering global UI refresh: $e');
      // Don't throw - this is not critical
    }
  }

  // Debug method to check database contents
  Future<void> _debugDatabaseContents(Database db) async {
    try {
      debugPrint('=== DEBUGGING DATABASE CONTENTS ===');

      // First, let's see what tables exist
      final tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table'");
      debugPrint('Available tables: ${tables.map((t) => t['name']).join(', ')}');

      // Check customers
      final customerCount = await db.rawQuery('SELECT COUNT(*) as count FROM customers');
      debugPrint('Customers: ${customerCount.first['count']} total');

      if ((customerCount.first['count'] as int) > 0) {
        final customerSample = await db.rawQuery('SELECT id, name, accountId FROM customers LIMIT 5');
        for (final customer in customerSample) {
          debugPrint('  Customer: id=${customer['id']}, name=${customer['name']}, accountId=${customer['accountId']}');
        }
      } else {
        // Check if there are any customers with different criteria
        final allCustomers = await db.rawQuery('SELECT id, name, accountId FROM customers');
        debugPrint('All customers in database: ${allCustomers.length}');
        for (final customer in allCustomers) {
          debugPrint('  Customer: id=${customer['id']}, name=${customer['name']}, accountId=${customer['accountId']}');
        }
      }

      // Check bills
      final billCount = await db.rawQuery('SELECT COUNT(*) as count FROM bills');
      debugPrint('Bills: ${billCount.first['count']} total');

      if ((billCount.first['count'] as int) > 0) {
        final billSample = await db.rawQuery('SELECT id, customerId, accountId, amount FROM bills LIMIT 5');
        for (final bill in billSample) {
          debugPrint('  Bill: id=${bill['id']}, customerId=${bill['customerId']}, accountId=${bill['accountId']}, amount=${bill['amount']}');
        }
      } else {
        // Check if there are any bills with different criteria
        final allBills = await db.rawQuery('SELECT id, customerId, accountId, amount FROM bills');
        debugPrint('All bills in database: ${allBills.length}');
        for (final bill in allBills) {
          debugPrint('  Bill: id=${bill['id']}, customerId=${bill['customerId']}, accountId=${bill['accountId']}, amount=${bill['amount']}');
        }
      }

      // Check payments
      final paymentCount = await db.rawQuery('SELECT COUNT(*) as count FROM payments');
      debugPrint('Payments: ${paymentCount.first['count']} total');

      if ((paymentCount.first['count'] as int) > 0) {
        final paymentSample = await db.rawQuery('SELECT id, customerId, accountId, amount FROM payments LIMIT 5');
        for (final payment in paymentSample) {
          debugPrint('  Payment: id=${payment['id']}, customerId=${payment['customerId']}, accountId=${payment['accountId']}, amount=${payment['amount']}');
        }
      } else {
        // Check if there are any payments with different criteria
        final allPayments = await db.rawQuery('SELECT id, customerId, accountId, amount FROM payments');
        debugPrint('All payments in database: ${allPayments.length}');
        for (final payment in allPayments) {
          debugPrint('  Payment: id=${payment['id']}, customerId=${payment['customerId']}, accountId=${payment['accountId']}, amount=${payment['amount']}');
        }
      }

      // Check settings
      final settings = await db.rawQuery('SELECT key, value, accountId FROM settings');
      debugPrint('Settings: ${settings.length} total');
      for (final setting in settings) {
        debugPrint('  Setting: key=${setting['key']}, value=${setting['value']}, accountId=${setting['accountId']}');
      }

      debugPrint('=== END DATABASE CONTENTS DEBUG ===');
    } catch (e) {
      debugPrint('Error debugging database contents: $e');
    }
  }

  // Method to detect account IDs in the restored database
  Future<List<String>> _detectAccountIdsInDatabase() async {
    try {
      final db = await DatabaseService.database;
      final accountIds = <String>{};

      // Check customers table for account IDs
      try {
        final customerResults = await db.rawQuery(
          'SELECT DISTINCT accountId FROM customers WHERE accountId IS NOT NULL'
        );
        for (final row in customerResults) {
          final accountId = row['accountId'] as String?;
          if (accountId != null && accountId.isNotEmpty) {
            accountIds.add(accountId);
          }
        }
      } catch (e) {
        debugPrint('Error checking customers table for account IDs: $e');
      }

      // Check bills table for account IDs
      try {
        final billResults = await db.rawQuery(
          'SELECT DISTINCT accountId FROM bills WHERE accountId IS NOT NULL'
        );
        for (final row in billResults) {
          final accountId = row['accountId'] as String?;
          if (accountId != null && accountId.isNotEmpty) {
            accountIds.add(accountId);
          }
        }
      } catch (e) {
        debugPrint('Error checking bills table for account IDs: $e');
      }

      // Check payments table for account IDs
      try {
        final paymentResults = await db.rawQuery(
          'SELECT DISTINCT accountId FROM payments WHERE accountId IS NOT NULL'
        );
        for (final row in paymentResults) {
          final accountId = row['accountId'] as String?;
          if (accountId != null && accountId.isNotEmpty) {
            accountIds.add(accountId);
          }
        }
      } catch (e) {
        debugPrint('Error checking payments table for account IDs: $e');
      }

      // Check expenses table for account IDs (if it exists)
      try {
        final tablesResult = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'"
        );
        if (tablesResult.isNotEmpty) {
          final expenseResults = await db.rawQuery(
            'SELECT DISTINCT accountId FROM expenses WHERE accountId IS NOT NULL'
          );
          for (final row in expenseResults) {
            final accountId = row['accountId'] as String?;
            if (accountId != null && accountId.isNotEmpty) {
              accountIds.add(accountId);
            }
          }
        }
      } catch (e) {
        debugPrint('Error checking expenses table for account IDs: $e');
      }

      final result = accountIds.toList();
      debugPrint('Detected account IDs in restored database: $result');
      return result;
    } catch (e) {
      debugPrint('Error detecting account IDs in database: $e');
      return [];
    }
  }

  /// Retrieve account name from backup database
  Future<String> _getAccountNameFromBackup(String dbPath, String accountId) async {
    Database? tempDb;
    try {
      debugPrint('Retrieving account name from backup database: $dbPath');

      // Add a small delay to ensure file is ready
      await Future.delayed(const Duration(milliseconds: 200));

      // Open database with timeout
      tempDb = await openDatabase(
        dbPath,
        readOnly: true,
        singleInstance: false, // Allow multiple connections
      );

      // Simple query with timeout
      final result = await tempDb.rawQuery(
        'SELECT value FROM settings WHERE key = ? LIMIT 1',
        ['accountName']
      ).timeout(const Duration(seconds: 5));

      debugPrint('Account name query result: $result');

      if (result.isNotEmpty) {
        final accountName = result.first['value'] as String;
        debugPrint('Found account name in backup: $accountName');
        return accountName;
      }

      debugPrint('No account name found in backup, using default');
      return 'Restored Account';

    } catch (e) {
      debugPrint('Error retrieving account name from backup: $e');
      return 'Restored Account';
    } finally {
      if (tempDb != null) {
        try {
          await tempDb.close();
          debugPrint('Backup database closed successfully');
        } catch (e) {
          debugPrint('Error closing backup database: $e');
        }
      }
    }
  }


}
