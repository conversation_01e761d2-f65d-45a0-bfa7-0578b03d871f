class Currency {
  final String code;
  final String symbol;
  final String name;
  final String countryCode;

  const Currency({
    required this.code,
    required this.symbol,
    required this.name,
    required this.countryCode,
  });

  // Factory method to create a Currency from a map
  factory Currency.fromMap(Map<String, dynamic> map) {
    return Currency(
      code: map['code'],
      symbol: map['symbol'],
      name: map['name'],
      countryCode: map['countryCode'] ?? '92',
    );
  }

  // Convert Currency to a map
  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'symbol': symbol,
      'name': name,
      'countryCode': countryCode,
    };
  }

  @override
  String toString() {
    return '$name ($symbol)';
  }
}

// List of common currencies
class Currencies {
  static const Currency indianRupee = Currency(
    code: 'INR',
    symbol: '₹',
    name: 'Indian Rupee',
    countryCode: '91',
  );

  static const Currency usDollar = Currency(
    code: 'USD',
    symbol: '\$',
    name: 'US Dollar',
    countryCode: '1',
  );

  static const Currency euro = Currency(
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    countryCode: '33',
  );

  static const Currency britishPound = Currency(
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    countryCode: '44',
  );

  static const Currency japaneseYen = Currency(
    code: 'JPY',
    symbol: '¥',
    name: 'Japanese Yen',
    countryCode: '81',
  );

  static const Currency canadianDollar = Currency(
    code: 'CAD',
    symbol: 'C\$',
    name: 'Canadian Dollar',
    countryCode: '1',
  );

  static const Currency australianDollar = Currency(
    code: 'AUD',
    symbol: 'A\$',
    name: 'Australian Dollar',
    countryCode: '61',
  );

  static const Currency swissFranc = Currency(
    code: 'CHF',
    symbol: 'Fr',
    name: 'Swiss Franc',
    countryCode: '41',
  );

  static const Currency chineseYuan = Currency(
    code: 'CNY',
    symbol: '¥',
    name: 'Chinese Yuan',
    countryCode: '86',
  );

  static const Currency pakistaniRupee = Currency(
    code: 'PKR',
    symbol: '₨',
    name: 'Pakistani Rupee',
    countryCode: '92',
  );

  static const Currency bangladeshiTaka = Currency(
    code: 'BDT',
    symbol: '৳',
    name: 'Bangladeshi Taka',
    countryCode: '880',
  );

  static const Currency nepaleseRupee = Currency(
    code: 'NPR',
    symbol: 'रू',
    name: 'Nepalese Rupee',
    countryCode: '977',
  );

  static const Currency sriLankanRupee = Currency(
    code: 'LKR',
    symbol: 'රු',
    name: 'Sri Lankan Rupee',
    countryCode: '94',
  );

  static const Currency singaporeDollar = Currency(
    code: 'SGD',
    symbol: 'S\$',
    name: 'Singapore Dollar',
    countryCode: '65',
  );

  static const Currency malaysianRinggit = Currency(
    code: 'MYR',
    symbol: 'RM',
    name: 'Malaysian Ringgit',
    countryCode: '60',
  );

  // List of all currencies
  static List<Currency> all = [
    indianRupee,
    usDollar,
    euro,
    britishPound,
    japaneseYen,
    canadianDollar,
    australianDollar,
    swissFranc,
    chineseYuan,
    pakistaniRupee,
    bangladeshiTaka,
    nepaleseRupee,
    sriLankanRupee,
    singaporeDollar,
    malaysianRinggit,
  ];

  // Get a currency by code
  static Currency? getByCode(String code) {
    try {
      return all.firstWhere((currency) => currency.code == code);
    } catch (e) {
      return null;
    }
  }

  // Get a currency by symbol
  static Currency? getBySymbol(String symbol) {
    try {
      return all.firstWhere((currency) => currency.symbol == symbol);
    } catch (e) {
      return null;
    }
  }
}
