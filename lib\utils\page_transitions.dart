import 'package:flutter/material.dart';

/// Custom page route that provides smooth fade transitions
class FadePageRoute<T> extends PageRoute<T> {
  final Widget Function(BuildContext) builder;
  final Duration duration;
  final Curve curve;

  FadePageRoute({
    required this.builder,
    this.duration = const Duration(milliseconds: 200),
    this.curve = Curves.easeOutCubic,
    super.settings,
  });

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => duration;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return builder(context);
  }

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // Use a curved animation for smoother transitions
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: curve,
      reverseCurve: Curves.easeInCubic,
    );

    // Use only fade transition for smoother transitions without vibration
    return FadeTransition(
      opacity: curvedAnimation,
      child: child,
    );
  }
}

/// Custom page route that provides smooth slide transitions
class SlidePageRoute<T> extends PageRoute<T> {
  final Widget Function(BuildContext) builder;
  final Duration duration;
  final SlideDirection direction;
  final Curve curve;
  final double slideOffset;

  SlidePageRoute({
    required this.builder,
    this.duration = const Duration(milliseconds: 200),
    this.direction = SlideDirection.right,
    this.curve = Curves.easeOutCubic,
    this.slideOffset = 0.1, // Further reduced for even smoother transitions
    super.settings,
  });

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => duration;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return builder(context);
  }

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // Use a curved animation for smoother transitions
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: curve,
      reverseCurve: Curves.easeInCubic,
    );

    var begin = _getBeginOffset();
    const end = Offset.zero;

    final tween = Tween(begin: begin, end: end);
    final offsetAnimation = curvedAnimation.drive(tween);

    // Combine slide and fade for smoother transitions without vibration
    return SlideTransition(
      position: offsetAnimation,
      child: FadeTransition(
        opacity: curvedAnimation,
        child: child,
      ),
    );
  }

  Offset _getBeginOffset() {
    switch (direction) {
      case SlideDirection.right:
        return Offset(-slideOffset, 0.0);
      case SlideDirection.left:
        return Offset(slideOffset, 0.0);
      case SlideDirection.up:
        return Offset(0.0, slideOffset);
      case SlideDirection.down:
        return Offset(0.0, -slideOffset);
    }
  }
}

/// Direction for slide transitions
enum SlideDirection {
  right,
  left,
  up,
  down,
}

/// Extension on Navigator to add custom transition methods
extension NavigatorExtension on NavigatorState {
  /// Push a route with a fade transition
  Future<T?> pushFade<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 220),
    Curve curve = Curves.easeOutCubic,
  }) {
    return push<T>(
      FadePageRoute<T>(
        builder: (_) => page,
        duration: duration,
        curve: curve,
      ),
    );
  }

  /// Push a route with a slide transition
  Future<T?> pushSlide<T extends Object?>(
    Widget page, {
    SlideDirection direction = SlideDirection.right,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
    double slideOffset = 0.1,
  }) {
    return push<T>(
      SlidePageRoute<T>(
        builder: (_) => page,
        direction: direction,
        duration: duration,
        curve: curve,
        slideOffset: slideOffset,
      ),
    );
  }

  /// Replace the current route with a fade transition
  Future<T?> pushReplacementFade<T extends Object?, TO extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
    TO? result,
  }) {
    return pushReplacement<T, TO>(
      FadePageRoute<T>(
        builder: (_) => page,
        duration: duration,
        curve: curve,
      ),
      result: result,
    );
  }

  /// Replace the current route with a slide transition
  Future<T?> pushReplacementSlide<T extends Object?, TO extends Object?>(
    Widget page, {
    SlideDirection direction = SlideDirection.right,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
    double slideOffset = 0.1,
    TO? result,
  }) {
    return pushReplacement<T, TO>(
      SlidePageRoute<T>(
        builder: (_) => page,
        direction: direction,
        duration: duration,
        curve: curve,
        slideOffset: slideOffset,
      ),
      result: result,
    );
  }

  /// Pop the current route with a smooth fade transition
  void popWithFade({
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
  }) {
    if (canPop()) {
      pop();
    }
  }

  /// Pop until a specific route name and then push a new route
  Future<T?> popAndPushFade<T extends Object?, TO extends Object?>(
    Widget page, {
    String? untilRouteName,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
    TO? result,
  }) {
    if (untilRouteName != null) {
      popUntil((route) => route.settings.name == untilRouteName);
    }

    return pushFade<T>(
      page,
      duration: duration,
      curve: curve,
    );
  }
}
