#!/bin/bash

echo "========================================"
echo "Building Optimized APK for Tubewell Water Billing"
echo "========================================"

echo ""
echo "Step 1: Getting dependencies..."
flutter pub get

echo ""
echo "Step 2: Generating app icons..."
flutter pub run flutter_launcher_icons

echo ""
echo "Step 3: Generating splash screen..."
flutter pub run flutter_native_splash:create

echo ""
echo "Step 4: Cleaning previous builds..."
flutter clean

echo ""
echo "Step 5: Getting dependencies again..."
flutter pub get

echo ""
echo "Step 6: Building split APKs for each architecture..."
flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info --split-per-abi --tree-shake-icons --dart-define=flutter.inspector.structuredErrors=false

echo ""
echo "Step 7: Building App Bundle (recommended for Play Store)..."
flutter build appbundle --release --shrink --obfuscate --split-debug-info=build/debug-info --tree-shake-icons --dart-define=flutter.inspector.structuredErrors=false

echo ""
echo "========================================"
echo "Build Complete!"
echo "========================================"
echo ""
echo "Split APK Locations:"
echo "- ARM64: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk"
echo "- ARM32: build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk"
echo "- x86_64: build/app/outputs/flutter-apk/app-x86_64-release.apk"
echo "App Bundle Location: build/app/outputs/bundle/release/app-release.aab"
echo ""
echo "Ultra Compact APK Features Enabled:"
echo "- Split APKs per architecture (much smaller sizes)"
echo "- Code shrinking and obfuscation"
echo "- Resource shrinking with density splits"
echo "- R8 full mode optimization"
echo "- ProGuard rules applied"
echo "- Tree-shaking icons"
echo "- Debug info split for smaller APK"
echo ""
echo "Choose the APK for your device architecture:"
echo "- Most modern phones: use ARM64 APK (~15-20MB)"
echo "- Older phones: use ARM32 APK (~15-20MB)"
echo "- Emulators/x86 devices: use x86_64 APK"
echo "- Play Store upload: use the .aab file"
echo "========================================"
