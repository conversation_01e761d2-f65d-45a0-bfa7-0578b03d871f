import 'dart:io';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_utility_service.dart';

class PdfBillDetailsService {
  static Future<File> generateBillPdf({
    required Bill bill,
    required Customer customer,
    required List<Payment> payments,
    PdfSettings? settings,
  }) async {
    try {
      // Starting bill PDF generation

      // Use provided settings or create default modern invoice settings
      final pdfSettings = (settings ?? PdfSettings.modernInvoice()).copyWith(
        headerStyle: HeaderStyle.compact, // Use compact header
        footerStyle: FooterStyle.compact, // Use compact footer
        additionalSettings: {
          'footerText': 'Thank you for your business!',
          'reference': 'INV-${bill.id}',
          // Retain other additional settings if modernInvoice() had more
          ...((settings ?? PdfSettings.modernInvoice()).additionalSettings ?? const {}),
        },
      );

      // PDF settings initialized

      // Define colors from settings
      final primaryColor = pdfSettings.primaryColor;
      final accentColor = pdfSettings.accentColor;
      final textColor = pdfSettings.textColor;

      // Get fonts - these are used by the template service
      // No need to store them locally

      // Define status colors
      final paidColor = PdfColor.fromHex('#2E7D32'); // Green
      final partialColor = PdfColor.fromHex('#1565C0'); // Blue
      final unpaidColor = PdfColor.fromHex('#C62828'); // Red
      final lightGreen = PdfBaseService.lightGreen;
      final lightBlue = PdfBaseService.lightBlue;
      // final lightRed = PdfBaseService.lightRed; // Removed unused variable

      // Create a PDF document with template
      final pdf = await PdfTemplateService.createPdfWithTemplate(
        title: 'BILL DETAILS',
        subtitle: 'Invoice #${bill.id}',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        watermarkText: pdfSettings.showWatermark
            ? (pdfSettings.watermarkText ?? 'COPY')
            : null,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      // PDF template created successfully, now adding content

      // Create content widgets
      final List<pw.Widget> contentWidgets = [
        // Customer Information
        PdfTemplateService.createStyledKeyValueTable(
          title: 'CUSTOMER INFORMATION',
          items: [
            {
              'Name': customer.name,
              'Phone': customer.contactNumber ?? 'N/A',
            },
          ],
          titleColor: accentColor,
          // Using default row colors (white/light grey) for a clean table look inside the card
        ),
        pw.SizedBox(height: 10),

        // Bill Information and calculations for summary
        () {
          // Calculate original duration and subtotal for display clarity
          final originalTotalDuration = bill.endTime.difference(bill.startTime);
          final originalTotalDurationHoursWhole = originalTotalDuration.inHours;
          final originalTotalDurationMinutes = originalTotalDuration.inMinutes % 60;
          final formattedOriginalDuration = '${originalTotalDurationHoursWhole}h ${originalTotalDurationMinutes}m';
          final subtotalPreDiscount = (originalTotalDuration.inMinutes / 60.0) * bill.hourlyRate;

          // Calculate monetary value of time discount
          // double timeDiscountValue = 0; // Unused variable
          if (bill.discountTime != null && bill.discountTime! > 0) {
            // timeDiscountValue = (bill.discountTime! / 60.0) * bill.hourlyRate; // Unused assignment
          }
          // final totalDiscountValue = timeDiscountValue + (bill.discountAmount ?? 0); // Removed unused variable


          final billInfoItems = <Map<String, String>>[
            {
              'Bill ID': '#${bill.id}',
              'Bill Date': DateFormat('dd MMM yyyy').format(bill.billDate),
              'Payment Status': bill.isPaid
                  ? 'PAID'
                  : (bill.isPartiallyPaid ? 'PARTIALLY PAID' : 'UNPAID'),
            },
            {
              'Start Time': DateFormat('hh:mm a').format(bill.startTime),
              'End Time': DateFormat('hh:mm a').format(bill.endTime),
              'Original Duration': formattedOriginalDuration,
            },
            {
              'Hourly Rate': 'Rs. ${bill.hourlyRate.toStringAsFixed(2)}/hr',
              'Subtotal (Pre-discount)': 'Rs. ${subtotalPreDiscount.toStringAsFixed(2)}',
            },
          ];

          // Add discounts if they exist
          if (bill.discountTime != null && bill.discountTime! > 0) {
            billInfoItems.add({'Time Discount': '${bill.discountTime!.toStringAsFixed(0)} minutes'});
          }
          if (bill.discountAmount != null && bill.discountAmount! > 0) {
            billInfoItems.add({'Amount Discount': 'Rs. ${bill.discountAmount!.toStringAsFixed(2)}'});
          }

          // Add Billed Duration (after time discount, before amount discount)
          // and Net Amount Payable (final amount after all discounts)
          billInfoItems.add({
            'Billed Duration': '${bill.durationHoursWhole}h ${bill.durationMinutes}m',
            'Net Amount Payable': 'Rs. ${bill.amount.toStringAsFixed(2)}',
          });

          if (bill.remarks != null && bill.remarks!.isNotEmpty) {
            billInfoItems.add({'Remarks': bill.remarks!});
          }

          return PdfTemplateService.createStyledKeyValueTable(
            title: 'BILL INFORMATION',
            items: billInfoItems,
            titleColor: bill.isPaid
                ? paidColor
                : (bill.isPartiallyPaid ? partialColor : unpaidColor),
            rowColorsCycle: [
              PdfColors.blueGrey50,
              PdfColors.teal50,
              PdfColors.lime50,
              PdfColors.orange50,
            ],
            // Text color will default to PdfBaseService.textColor for better contrast
            // keyTextColor and valueTextColor are removed to use default.
          );
        }(), // End of Bill Information IIFE

        pw.SizedBox(height: 10),

        pw.NewPage(), // Force new page after Bill Information

        // Payment Information if any payments exist
        if (payments.isNotEmpty) ...[
          PdfTemplateService.createSectionTitle(
            'PAYMENT HISTORY',
            color: accentColor,
          ),
          PdfTemplateService.createDataTable(
            headers: ['ID', 'Date', 'Amount', 'Method', 'Remarks'],
            data: payments
                .map((payment) => [
                      '#${payment.id}',
                      DateFormat('dd/MM/yyyy').format(payment.paymentDate),
                      'Rs. ${payment.amount.toStringAsFixed(2)}',
                      payment.paymentMethod ?? 'N/A',
                      payment.remarks ?? '',
                    ])
                .toList(),
            headerColor: lightGreen,
            alternateColor: lightBlue,
            zebra: true,
          ),
          pw.SizedBox(height: 10),
        ],
        // Removed the else ...[] block and the redundant SizedBox comment
        
        // pw.NewPage(), // REMOVED: This was causing a blank page if payment history was short or absent.

        // Summary Section - now using createStyledKeyValueTable
        () {
          // Recalculate subtotalPreDiscount and timeDiscountValue if not accessible from outer scope
          // For simplicity, assuming they are accessible or recalculated if needed.
          // If they are not accessible, these calculations must be repeated here.
          // For this example, I'll assume subtotalPreDiscount and totalDiscountValue are available
          // from the previous IIFE or passed down.
          // Let's refine this by ensuring values are correctly scoped or recalculated.

          final originalTotalDuration = bill.endTime.difference(bill.startTime);
          final subtotalPreDiscountForSummary = (originalTotalDuration.inMinutes / 60.0) * bill.hourlyRate;
          
          double timeDiscountValueForSummary = 0;
          if (bill.discountTime != null && bill.discountTime! > 0) {
            timeDiscountValueForSummary = (bill.discountTime! / 60.0) * bill.hourlyRate;
          }
          final totalDiscountsForSummary = timeDiscountValueForSummary + (bill.discountAmount ?? 0);

          final summaryItems = <Map<String, String>>[
            {'Subtotal (Pre-discount)': 'Rs. ${subtotalPreDiscountForSummary.toStringAsFixed(2)}'},
          ];

          if (timeDiscountValueForSummary > 0) {
            summaryItems.add({'Value of Time Discount': 'Rs. ${timeDiscountValueForSummary.toStringAsFixed(2)}'});
          }
          if (bill.discountAmount != null && bill.discountAmount! > 0) {
            summaryItems.add({'Amount Discount Given': 'Rs. ${bill.discountAmount!.toStringAsFixed(2)}'});
          }
           if (totalDiscountsForSummary > 0) {
            summaryItems.add({'Total Discounts': 'Rs. ${totalDiscountsForSummary.toStringAsFixed(2)}'});
          }

          summaryItems.addAll([
            {'Net Amount Payable': 'Rs. ${bill.amount.toStringAsFixed(2)}'},
            {'Amount Paid': 'Rs. ${bill.isPaid ? bill.amount.toStringAsFixed(2) : (bill.partialAmount?.toStringAsFixed(2) ?? '0.00')}'},
            {'Balance Due': 'Rs. ${bill.outstandingAmount.toStringAsFixed(2)}'},
          ]);

          return PdfTemplateService.createStyledKeyValueTable(
            title: 'PAYMENT SUMMARY',
            items: summaryItems,
            titleColor: bill.isPaid
                ? paidColor
                : (bill.isPartiallyPaid ? partialColor : unpaidColor),
            // Using default row colors for summary, or choose specific ones
            rowEvenColor: PdfColors.white,
            rowOddColor: PdfColor.fromHex('#F5F5F5'), // Light grey for zebra
            keyTextColor: textColor, // Default text color for keys
            valueTextColor: textColor, // Default text color for values
             showRowDividers: true, // Show dividers for clarity in summary
          );
        }(), // End of Payment Summary IIFE
      ];

      // Add page with template using settings
      await PdfTemplateService.addPageWithTemplate(
        pdf: pdf,
        contentWidgets: PdfTemplateService.createContentSection(
          contentStyle: pdfSettings.contentStyle,
          contentWidgets: contentWidgets,
          primaryColor: primaryColor,
          accentColor: accentColor,
          textColor: textColor,
        ),
        title: 'BILL DETAILS',
        subtitle: 'Invoice #${bill.id}',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        contentStyle: pdfSettings.contentStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        headerData: {
          ...pdfSettings.getHeaderData(),
          'reference': 'INV-${bill.id}',
          'customerInfo': 'Customer: ${customer.name}',
        },
        footerData: pdfSettings.getFooterData(),
      );

      // Content added to PDF successfully

      // Create uniquely named file
      final fileName =
          'bill_details_${bill.id}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      // Save the PDF to a temporary file
      final file = await PdfUtilityService.savePdfToTemp(pdf, fileName);

      return file;
    } catch (e) {
      // Just rethrow the error to be handled by the caller
      rethrow;
    }
  }
}
