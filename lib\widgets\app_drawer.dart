import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../services/account_service.dart';
import '../models/account.dart';
import '../widgets/bottom_navigation.dart';
import '../utils/page_transitions.dart';

class AppDrawer extends StatefulWidget {
  final String currentScreen;

  const AppDrawer({super.key, required this.currentScreen});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  Account? _currentAccount;
  List<Account> _availableAccounts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      await AccountService.initialize();
      setState(() {
        _currentAccount = AccountService.currentAccount;
        _availableAccounts = AccountService.accounts;
        _isLoading = false;
      });
    } catch (e) {
      // Error handled by setting isLoading to false
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _switchAccount(Account account) async {
    final logger = Logger();

    // If already on this account, just close the drawer
    if (_currentAccount?.id == account.id) {
      Navigator.pop(context);
      return;
    }

    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    try {
      logger.i('Switching to account: ${account.name} (${account.id})');

      // Switch account using the service
      await AccountService.switchAccount(account.id);

      // Check if widget is still mounted after async operation
      if (!mounted) return;

      // Update UI state
      setState(() {
        _currentAccount = account;
        _isLoading = false;
      });

      // Close the drawer
      // ignore: use_build_context_synchronously
      Navigator.pop(context);

      // Show success message
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Switched to account: ${account.name}')),
      );

      // Get the appropriate tab index based on the current screen
      int initialIndex = _getTabIndexFromScreenName(widget.currentScreen);

      // Navigate to BottomNavigation with the appropriate initial index using smooth transition
      // ignore: use_build_context_synchronously
      Navigator.of(context).pushReplacementFade(
        BottomNavigation(initialIndex: initialIndex),
        duration: const Duration(milliseconds: 200),
      );
    } catch (e) {
      logger.e('Error switching account: $e');

      // Check if widget is still mounted after async operation
      if (!mounted) return;

      // Hide loading indicator
      setState(() {
        _isLoading = false;
      });

      // Show error message
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error switching account: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  // Helper method to get the tab index from screen name
  int _getTabIndexFromScreenName(String screenName) {
    switch (screenName) {
      case 'dashboard':
      case 'summary':
        return 0; // Account Summary tab
      case 'transactions':
        return 1; // Bills/Transactions tab
      case 'customers':
        return 2; // Customers tab
      case 'payments':
        return 3; // Payments tab
      case 'expenses':
        return 4; // Expenses tab
      case 'settings':
        return 0; // Navigate to Home tab for settings
      default:
        return 0; // Default to Summary tab
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Tubewell Water Billing',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Manage your water billing efficiently',
                  style: TextStyle(
                    color: Colors.white.withAlpha(204), // 0.8 opacity
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                if (_isLoading)
                  const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                else if (_currentAccount != null)
                  Flexible(
                    child: GestureDetector(
                      onTap: () => _editCurrentAccount(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(51), // 0.2 opacity
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.account_circle,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                _currentAccount!.name,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 4),
                            const Icon(
                              Icons.edit,
                              color: Colors.white,
                              size: 12,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.home,
            title: 'Home',
            isSelected: widget.currentScreen == 'dashboard' || widget.currentScreen == 'summary',
            onTap: () {
              if (widget.currentScreen != 'dashboard' && widget.currentScreen != 'summary') {
                Navigator.pop(context);
                Navigator.of(context).pushReplacementFade(
                  const BottomNavigation(initialIndex: 0),
                  duration: const Duration(milliseconds: 200),
                );
              } else {
                Navigator.pop(context);
              }
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.receipt_long,
            title: 'Bills',
            isSelected: widget.currentScreen == 'transactions',
            onTap: () {
              if (widget.currentScreen != 'transactions') {
                Navigator.pop(context);
                Navigator.of(context).pushReplacementFade(
                  const BottomNavigation(initialIndex: 1),
                  duration: const Duration(milliseconds: 200),
                );
              } else {
                Navigator.pop(context);
              }
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.people,
            title: 'Customers',
            isSelected: widget.currentScreen == 'customers',
            onTap: () {
              if (widget.currentScreen != 'customers') {
                Navigator.pop(context);
                Navigator.of(context).pushReplacementFade(
                  const BottomNavigation(initialIndex: 2),
                  duration: const Duration(milliseconds: 200),
                );
              } else {
                Navigator.pop(context);
              }
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.payment,
            title: 'Payments',
            isSelected: widget.currentScreen == 'payments',
            onTap: () {
              if (widget.currentScreen != 'payments') {
                Navigator.pop(context);
                Navigator.of(context).pushReplacementFade(
                  const BottomNavigation(initialIndex: 3),
                  duration: const Duration(milliseconds: 200),
                );
              } else {
                Navigator.pop(context);
              }
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.money_off,
            title: 'Expenses',
            isSelected: widget.currentScreen == 'expenses',
            onTap: () {
              if (widget.currentScreen != 'expenses') {
                Navigator.pop(context);
                Navigator.of(context).pushReplacementFade(
                  const BottomNavigation(initialIndex: 4),
                  duration: const Duration(milliseconds: 200),
                );
              } else {
                Navigator.pop(context);
              }
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.notifications_active,
            title: 'Payment Reminders',
            isSelected: widget.currentScreen == 'reminders',
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/reminders');
            },
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.settings,
            title: 'Settings',
            isSelected: widget.currentScreen == 'settings',
            onTap: () {
              if (widget.currentScreen != 'settings') {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/settings');
              } else {
                Navigator.pop(context);
              }
            },
          ),
          const Divider(),
          if (_availableAccounts.length > 1) ...[
            const Divider(),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                'Switch Account',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            ..._availableAccounts.map((account) {
              final isCurrentAccount = _currentAccount?.id == account.id;
              return ListTile(
                leading: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isCurrentAccount
                        ? Colors.green.shade100
                        : Colors.grey.shade100,
                  ),
                  child: Center(
                    child: Icon(
                      isCurrentAccount
                          ? Icons.check_circle
                          : Icons.account_circle,
                      color: isCurrentAccount ? Colors.green : Colors.grey,
                      size: 20,
                    ),
                  ),
                ),
                title: Text(
                  account.name,
                  style: TextStyle(
                    fontWeight:
                        isCurrentAccount ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                subtitle: account.description != null &&
                        account.description!.isNotEmpty
                    ? Text(
                        account.description!,
                        style: const TextStyle(fontSize: 12),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )
                    : null,
                selected: isCurrentAccount,
                onTap: () => _switchAccount(account),
              );
            }),
          ],
        ],
      ),
    );
  }

  Future<void> _editCurrentAccount() async {
    if (_currentAccount == null) return;

    final nameController = TextEditingController(text: _currentAccount!.name);
    final descriptionController =
        TextEditingController(text: _currentAccount!.description ?? '');

    final result = await showDialog<Map<String, String?>>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Edit Account'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Account Name',
                  hintText: 'Enter account name',
                  helperText: 'Required',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  hintText: 'Enter account description',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Validate input before closing dialog
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Account name cannot be empty')),
                  );
                  return;
                }

                // Check if name is already taken by another account
                final newName = nameController.text.trim();
                if (newName != _currentAccount!.name &&
                    _availableAccounts.any((a) =>
                        a.id != _currentAccount!.id && a.name == newName)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Account with this name already exists')),
                  );
                  return;
                }

                Navigator.of(context).pop({
                  'name': newName,
                  'description': descriptionController.text.trim(),
                });
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );

    if (result == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // Update the account
      final updatedAccount = Account(
        id: _currentAccount!.id,
        name: result['name']!,
        description: result['description'],
        createdAt: _currentAccount!.createdAt,
        lastAccessed: _currentAccount!.lastAccessed,
      );

      await AccountService.updateAccount(updatedAccount);

      // Refresh accounts
      await _loadAccounts();

      // Check if widget is still mounted after async operations
      if (!mounted) return;

      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Account "${updatedAccount.name}" updated')),
      );
    } catch (e) {
      // Check if widget is still mounted after async operation
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating account: $e')),
      );
    }
  }

  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).primaryColor : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Theme.of(context).primaryColor : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      selected: isSelected,
      onTap: onTap,
    );
  }
}
