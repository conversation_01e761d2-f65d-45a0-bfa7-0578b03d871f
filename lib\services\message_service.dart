import 'package:url_launcher/url_launcher.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/services/settings_service.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:intl/intl.dart';

/// A service for handling messaging functionality (WhatsApp and SMS)
class MessageService {
  // Default templates
  static String getDefaultBillTemplate() {
    return '''Dear {customer_name},

A new bill has been generated for your account:

Bill #{bill_id}
Date: {bill_date}
Period: {start_date} - {end_date}
Duration: {duration}
Amount: {amount}

{status}
{paid_amount}
{remaining_amount}

Thank you for your business.''';
  }

  static String getDefaultPaymentTemplate() {
    return '''Dear {customer_name},

We have received your payment:

Payment #{payment_id}
Date: {payment_date}
Amount: {amount}
Method: {payment_method}
{bill_id}
{remaining_credit}

Thank you for your payment.''';
  }

  static String getDefaultReminderTemplate() {
    return '''Dear {customer_name},

This is a friendly reminder about the following unpaid bill(s):

{bill_details}

{total_amount}

Please make payment at your earliest convenience.

Thank you for your business.''';
  }

  /// Get custom template if available, otherwise return default
  static Future<String> _getTemplate(String templateType) async {
    try {
      // Get current account ID
      final accountId = DatabaseService.getCurrentAccountId() ?? '';

      // Get custom template if it exists
      final customTemplate = await SettingsService.getMessageTemplate(
        accountId: accountId,
        templateType: templateType,
      );

      // Return custom template if it exists, otherwise return default
      if (customTemplate != null && customTemplate.isNotEmpty) {
        return customTemplate;
      }

      // Return default template
      switch (templateType) {
        case 'bill':
          return getDefaultBillTemplate();
        case 'payment':
          return getDefaultPaymentTemplate();
        case 'reminder':
          return getDefaultReminderTemplate();
        default:
          return '';
      }
    } catch (e) {
      // Return default template on error
      switch (templateType) {
        case 'bill':
          return getDefaultBillTemplate();
        case 'payment':
          return getDefaultPaymentTemplate();
        case 'reminder':
          return getDefaultReminderTemplate();
        default:
          return '';
      }
    }
  }

  /// Format a message for a new bill
  static Future<String> formatBillMessage(Bill bill, Customer customer) async {
    final template = await _getTemplate('bill');
    return formatBillMessageWithTemplate(bill, customer, template);
  }

  /// Format a bill message with a specific template
  static String formatBillMessageWithTemplate(
      Bill bill, Customer customer, String template) {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final timeFormat = DateFormat('hh:mm a');

    // Replace placeholders with actual values
    String message = template;

    // Basic replacements
    message = message.replaceAll('{customer_name}', customer.name);
    message = message.replaceAll('{bill_id}', bill.id.toString());
    message =
        message.replaceAll('{bill_date}', dateFormat.format(bill.billDate));
    message = message.replaceAll('{start_date}',
        '${dateFormat.format(bill.startTime)} ${timeFormat.format(bill.startTime)}');
    message = message.replaceAll('{end_date}',
        '${dateFormat.format(bill.endTime)} ${timeFormat.format(bill.endTime)}');
    message = message.replaceAll(
        '{duration}', '${bill.durationHours.toStringAsFixed(1)} hours');
    message = message.replaceAll(
        '{amount}', CurrencyService.formatCurrency(bill.amount));

    // Conditional replacements
    String status = '';
    String paidAmount = '';
    String remainingAmount = '';

    if (bill.isPaid) {
      status = 'Status: Paid';
    } else if (bill.isPartiallyPaid) {
      status = 'Status: Partially Paid';
      paidAmount =
          'Paid Amount: ${CurrencyService.formatCurrency(bill.partialAmount ?? 0)}';
      remainingAmount =
          'Remaining: ${CurrencyService.formatCurrency(bill.outstandingAmount)}';
    } else {
      status =
          'Status: Unpaid\nPlease make payment at your earliest convenience.';
    }

    message = message.replaceAll('{status}', status);
    message = message.replaceAll('{paid_amount}', paidAmount);
    message = message.replaceAll('{remaining_amount}', remainingAmount);

    return message;
  }

  /// Format a message for a new payment
  static Future<String> formatPaymentMessage(Payment payment, Customer customer,
      {double? remainingCredit}) async {
    final template = await _getTemplate('payment');
    return formatPaymentMessageWithTemplate(payment, customer, template,
        remainingCredit: remainingCredit);
  }

  /// Format a payment message with a specific template
  static String formatPaymentMessageWithTemplate(
      Payment payment, Customer customer, String template,
      {double? remainingCredit}) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    // Replace placeholders with actual values
    String message = template;

    // Basic replacements
    message = message.replaceAll('{customer_name}', customer.name);
    message = message.replaceAll('{payment_id}', payment.id.toString());
    message = message.replaceAll(
        '{payment_date}', dateFormat.format(payment.paymentDate));
    message = message.replaceAll(
        '{amount}', CurrencyService.formatCurrency(payment.amount));
    message =
        message.replaceAll('{payment_method}', payment.paymentMethod ?? 'Cash');

    // Conditional replacements
    String billId = '';
    String remainingCreditText = '';

    if (payment.billId > 0) {
      billId = 'For Bill #${payment.billId}';
    }

    if (remainingCredit != null && remainingCredit > 0) {
      remainingCreditText =
          'Remaining Credit: ${CurrencyService.formatCurrency(remainingCredit)}';
    }

    message = message.replaceAll('{bill_id}', billId);
    message = message.replaceAll('{remaining_credit}', remainingCreditText);

    return message;
  }

  /// Launch WhatsApp with a pre-filled message
  static Future<bool> sendWhatsAppMessage(
      String phoneNumber, String message) async {
    // Format the phone number (remove any non-digit characters)
    String formattedNumber = phoneNumber.replaceAll(RegExp(r'[^0-9]'), '');

    // Make sure the phone number has a country code
    if (!formattedNumber.startsWith('+')) {
      // Get country code from currency settings
      String countryCode = CurrencyService.getCountryCode();
      
      // Add country code if not already present
      if (!formattedNumber.startsWith(countryCode)) {
        formattedNumber = '$countryCode$formattedNumber';
      }
    }

    // Create the WhatsApp URL
    final whatsappUrl = Uri.parse(
        'https://wa.me/$formattedNumber?text=${Uri.encodeComponent(message)}');

    // Check if WhatsApp is installed
    if (await canLaunchUrl(whatsappUrl)) {
      return launchUrl(whatsappUrl, mode: LaunchMode.externalApplication);
    } else {
      return false;
    }
  }

  /// Launch SMS app with a pre-filled message
  static Future<bool> sendSmsMessage(String phoneNumber, String message) async {
    // Create the SMS URL
    final smsUrl =
        Uri.parse('sms:$phoneNumber?body=${Uri.encodeComponent(message)}');

    // Check if SMS app can be launched
    if (await canLaunchUrl(smsUrl)) {
      return launchUrl(smsUrl);
    } else {
      return false;
    }
  }

  /// Format a reminder message for unpaid bills
  static Future<String> formatReminderMessage(
      List<Bill> bills, Customer customer,
      {String? customMessage}) async {
    final template = await _getTemplate('reminder');
    return formatReminderMessageWithTemplate(bills, customer, template,
        customMessage: customMessage);
  }

  /// Format a reminder message with a specific template
  static String formatReminderMessageWithTemplate(
      List<Bill> bills, Customer customer, String template,
      {String? customMessage}) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    // Replace placeholders with actual values
    String message = template;

    // Basic replacements
    message = message.replaceAll('{customer_name}', customer.name);

    // Add custom message if provided
    if (customMessage != null && customMessage.isNotEmpty) {
      message = message.replaceAll(
          'This is a friendly reminder about the following unpaid bill(s):',
          customMessage);
    }

    // Calculate total amount due
    double totalAmount = 0;

    // Build bill details
    String billDetails = '';

    // Add details for each bill
    for (int i = 0; i < bills.length; i++) {
      final bill = bills[i];
      final outstandingAmount = bill.outstandingAmount;
      totalAmount += outstandingAmount;

      billDetails += 'Bill #${bill.id}\n';
      billDetails += 'Date: ${dateFormat.format(bill.billDate)}\n';

      if (bill.isPartiallyPaid) {
        billDetails += 'Status: Partially Paid\n';
        billDetails +=
            'Remaining: ${CurrencyService.formatCurrency(outstandingAmount)}\n';
      } else {
        billDetails +=
            'Amount: ${CurrencyService.formatCurrency(bill.amount)}\n';
      }

      // Add separator between bills
      if (i < bills.length - 1) {
        billDetails += '\n------------------\n\n';
      }
    }

    message = message.replaceAll('{bill_details}', billDetails);

    // Add total amount due if there are multiple bills
    String totalAmountText = '';
    if (bills.length > 1) {
      totalAmountText =
          'Total Amount Due: ${CurrencyService.formatCurrency(totalAmount)}';
    }

    message = message.replaceAll('{total_amount}', totalAmountText);

    return message;
  }

  /// Format a single bill reminder message
  static Future<String> formatSingleBillReminderMessage(
      Bill bill, Customer customer,
      {String? customMessage}) async {
    return formatReminderMessage([bill], customer,
        customMessage: customMessage);
  }
}
