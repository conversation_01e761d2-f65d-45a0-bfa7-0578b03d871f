import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';
import 'package:tubewell_water_billing/services/message_service.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/settings_service.dart';

class MessageTemplatesScreen extends StatefulWidget {
  const MessageTemplatesScreen({super.key});

  @override
  State<MessageTemplatesScreen> createState() => _MessageTemplatesScreenState();
}

class _MessageTemplatesScreenState extends State<MessageTemplatesScreen>
    with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  late TabController _tabController;

  // Template controllers
  final TextEditingController _billTemplateController = TextEditingController();
  final TextEditingController _paymentTemplateController =
      TextEditingController();
  final TextEditingController _reminderTemplateController =
      TextEditingController();

  // Preview data
  final Customer _sampleCustomer = Customer(
    id: 0,
    name: 'John Doe',
    contactNumber: '+919876543210',
    createdAt: DateTime.now(),
  );

  late Bill _sampleBill;
  late Payment _samplePayment;

  // Default templates
  String _defaultBillTemplate = '';
  String _defaultPaymentTemplate = '';
  String _defaultReminderTemplate = '';

  // Custom templates
  String? _customBillTemplate;
  String? _customPaymentTemplate;
  String? _customReminderTemplate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeSampleData();
    _loadTemplates();
  }

  void _initializeSampleData() {
    // Create sample bill
    _sampleBill = Bill(
      id: 1001,
      customerId: _sampleCustomer.id,
      billDate: DateTime.now(),
      startTime: DateTime.now().subtract(const Duration(days: 1)),
      endTime: DateTime.now(),
      durationHours: 24,
      durationHoursWhole: 24,
      durationMinutes: 0,
      hourlyRate: 50,
      amount: 1200,
      isPaid: false,
      isPartiallyPaid: false,
      partialAmount: 0,
    );

    // Create sample payment
    _samplePayment = Payment(
      id: 2001,
      customerId: _sampleCustomer.id,
      billId: _sampleBill.id,
      amount: 1000,
      paymentDate: DateTime.now(),
      paymentMethod: 'Cash',
      remarks: 'Regular payment',
    );
  }

  Future<void> _loadTemplates() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize settings service
      await SettingsService.initialize();

      // Get current account ID
      final accountId = DatabaseService.getCurrentAccountId() ?? '';

      // Load custom templates if they exist
      final billTemplate = await SettingsService.getMessageTemplate(
        accountId: accountId,
        templateType: 'bill',
      );
      _customBillTemplate = billTemplate;

      final paymentTemplate = await SettingsService.getMessageTemplate(
        accountId: accountId,
        templateType: 'payment',
      );
      _customPaymentTemplate = paymentTemplate;

      final reminderTemplate = await SettingsService.getMessageTemplate(
        accountId: accountId,
        templateType: 'reminder',
      );
      _customReminderTemplate = reminderTemplate;

      // Get default templates
      _defaultBillTemplate = MessageService.getDefaultBillTemplate();
      _defaultPaymentTemplate = MessageService.getDefaultPaymentTemplate();
      _defaultReminderTemplate = MessageService.getDefaultReminderTemplate();

      // Set controller text
      _billTemplateController.text =
          _customBillTemplate ?? _defaultBillTemplate;
      _paymentTemplateController.text =
          _customPaymentTemplate ?? _defaultPaymentTemplate;
      _reminderTemplateController.text =
          _customReminderTemplate ?? _defaultReminderTemplate;

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading message templates: $e'),
          backgroundColor: Colors.red,
        ),
      );

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveTemplate(String templateType, String template) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Get current account ID
      final accountId = DatabaseService.getCurrentAccountId() ?? '';

      // Save template
      await SettingsService.saveMessageTemplate(
        accountId: accountId,
        templateType: templateType,
        template: template,
      );

      // Update local variables
      switch (templateType) {
        case 'bill':
          _customBillTemplate = template;
          break;
        case 'payment':
          _customPaymentTemplate = template;
          break;
        case 'reminder':
          _customReminderTemplate = template;
          break;
      }

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Template saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving template: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _resetTemplate(String templateType) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Get current account ID
      final accountId = DatabaseService.getCurrentAccountId() ?? '';

      // Delete custom template
      await SettingsService.deleteMessageTemplate(
        accountId: accountId,
        templateType: templateType,
      );

      // Update local variables and controllers
      switch (templateType) {
        case 'bill':
          _customBillTemplate = null;
          _billTemplateController.text = _defaultBillTemplate;
          break;
        case 'payment':
          _customPaymentTemplate = null;
          _paymentTemplateController.text = _defaultPaymentTemplate;
          break;
        case 'reminder':
          _customReminderTemplate = null;
          _reminderTemplateController.text = _defaultReminderTemplate;
          break;
      }

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Template reset to default'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error resetting template: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _getPreviewForTemplate(String templateType, String template) {
    try {
      switch (templateType) {
        case 'bill':
          return MessageService.formatBillMessageWithTemplate(
            _sampleBill,
            _sampleCustomer,
            template,
          );
        case 'payment':
          return MessageService.formatPaymentMessageWithTemplate(
            _samplePayment,
            _sampleCustomer,
            template,
            remainingCredit: 200,
          );
        case 'reminder':
          return MessageService.formatReminderMessageWithTemplate(
            [_sampleBill],
            _sampleCustomer,
            template,
          );
        default:
          return 'Preview not available';
      }
    } catch (e) {
      return 'Error generating preview: $e';
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _billTemplateController.dispose();
    _paymentTemplateController.dispose();
    _reminderTemplateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Message Templates',
        showPdfOption: false,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                TabBar(
                  controller: _tabController,
                  labelColor: Theme.of(context).primaryColor,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Theme.of(context).primaryColor,
                  tabs: const [
                    Tab(text: 'Bill', icon: Icon(Icons.receipt)),
                    Tab(text: 'Payment', icon: Icon(Icons.payment)),
                    Tab(text: 'Reminder', icon: Icon(Icons.notifications)),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildTemplateTab(
                        templateType: 'bill',
                        controller: _billTemplateController,
                        title: 'Bill Notification Template',
                        description:
                            'Customize the message sent to customers when a new bill is generated.',
                        placeholders: [
                          '{customer_name}',
                          '{bill_id}',
                          '{bill_date}',
                          '{start_date}',
                          '{end_date}',
                          '{duration}',
                          '{amount}',
                          '{status}',
                          '{paid_amount}',
                          '{remaining_amount}',
                        ],
                      ),
                      _buildTemplateTab(
                        templateType: 'payment',
                        controller: _paymentTemplateController,
                        title: 'Payment Notification Template',
                        description:
                            'Customize the message sent to customers when a payment is recorded.',
                        placeholders: [
                          '{customer_name}',
                          '{payment_id}',
                          '{payment_date}',
                          '{amount}',
                          '{payment_method}',
                          '{bill_id}',
                          '{remaining_credit}',
                        ],
                      ),
                      _buildTemplateTab(
                        templateType: 'reminder',
                        controller: _reminderTemplateController,
                        title: 'Payment Reminder Template',
                        description:
                            'Customize the message sent to customers as a reminder for unpaid bills.',
                        placeholders: [
                          '{customer_name}',
                          '{bill_id}',
                          '{bill_date}',
                          '{amount}',
                          '{status}',
                          '{remaining_amount}',
                          '{total_amount}',
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildTemplateTab({
    required String templateType,
    required TextEditingController controller,
    required String title,
    required String description,
    required List<String> placeholders,
  }) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Available Placeholders:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: placeholders.map((placeholder) {
                      return InkWell(
                        onTap: () {
                          final currentPosition =
                              controller.selection.baseOffset;
                          final text = controller.text;
                          final newText = text.substring(0, currentPosition) +
                              placeholder +
                              text.substring(currentPosition);
                          controller.text = newText;
                          controller.selection = TextSelection.fromPosition(
                            TextPosition(
                                offset: currentPosition + placeholder.length),
                          );
                        },
                        child: Chip(
                          label: Text(placeholder),
                          backgroundColor: Colors.blue.shade50,
                          side: BorderSide(color: Colors.blue.shade200),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Template Editor',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: controller,
                    maxLines: 10,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Enter your message template here...',
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: OutlinedButton.icon(
                            onPressed: () => _resetTemplate(templateType),
                            icon: const Icon(Icons.refresh, size: 18),
                            label: const Text('Reset'),
                          ),
                        ),
                      ),
                      Flexible(
                        flex: 1,
                        child: ElevatedButton.icon(
                          onPressed: () =>
                              _saveTemplate(templateType, controller.text),
                          icon: const Icon(Icons.save, size: 18),
                          label: const Text('Save'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Message Preview',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.copy),
                        onPressed: () {
                          final preview = _getPreviewForTemplate(
                              templateType, controller.text);
                          Clipboard.setData(ClipboardData(text: preview));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Preview copied to clipboard')),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                        _getPreviewForTemplate(templateType, controller.text)),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
