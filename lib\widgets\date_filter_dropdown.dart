import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Enum representing different date range options
enum DateFilterOption {
  today(label: 'Today', icon: Icons.today),
  last7Days(label: 'Last 7 Days', icon: Icons.view_week),
  last30Days(label: 'Last 30 Days', icon: Icons.calendar_view_month),
  last3Months(label: 'Last 3 Months', icon: Icons.calendar_view_week),
  last6Months(label: 'Last 6 Months', icon: Icons.date_range_outlined),
  thisMonth(label: 'This Month', icon: Icons.calendar_month),
  lastMonth(label: 'Last Month', icon: Icons.skip_previous),
  thisYear(label: 'This Year', icon: Icons.calendar_today),
  lastYear(label: 'Last Year', icon: Icons.history),
  custom(label: 'Custom Range', icon: Icons.date_range);

  final String label;
  final IconData icon;

  const DateFilterOption({
    required this.label,
    required this.icon,
  });
}

/// A callback function type for when date range changes
typedef DateRangeChangedCallback = void Function(DateTime? startDate, DateTime? endDate, DateFilterOption selectedOption);

/// A reusable widget for date range filtering with predefined options
class DateFilterDropdown extends StatefulWidget {
  /// The currently selected date range option
  final DateFilterOption selectedOption;

  /// The current start date
  final DateTime? startDate;

  /// The current end date
  final DateTime? endDate;

  /// Callback when date range changes
  final DateRangeChangedCallback onDateRangeChanged;

  /// Background color for the dropdown button
  final Color? backgroundColor;

  /// Text color for the dropdown button
  final Color? textColor;

  /// Icon color for the dropdown button
  final Color? iconColor;

  /// Border color for the dropdown button
  final Color? borderColor;

  const DateFilterDropdown({
    super.key,
    required this.selectedOption,
    required this.startDate,
    required this.endDate,
    required this.onDateRangeChanged,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.borderColor,
  });

  @override
  State<DateFilterDropdown> createState() => _DateFilterDropdownState();
}

class _DateFilterDropdownState extends State<DateFilterDropdown> {
  late DateFilterOption _selectedOption;

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.selectedOption;
  }

  @override
  void didUpdateWidget(DateFilterDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedOption != widget.selectedOption) {
      _selectedOption = widget.selectedOption;
    }
  }

  /// Apply the selected date filter option
  void _applyDateFilter(DateFilterOption option) async {
    // Update the selected option immediately for better UI feedback
    setState(() {
      _selectedOption = option;
    });

    DateTime? startDate;
    DateTime? endDate;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (option) {
      case DateFilterOption.today:
        startDate = today;
        endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last7Days:
        startDate = today.subtract(const Duration(days: 6));
        endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last30Days:
        startDate = today.subtract(const Duration(days: 29));
        endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last3Months:
        startDate = DateTime(today.year, today.month - 3, today.day);
        // Handle year boundary
        if (startDate.month <= 0) {
          startDate = DateTime(today.year - 1, 12 + startDate.month, today.day);
        }
        endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.last6Months:
        startDate = DateTime(today.year, today.month - 6, today.day);
        // Handle year boundary
        if (startDate.month <= 0) {
          startDate = DateTime(today.year - 1, 12 + startDate.month, today.day);
        }
        endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
        break;
      case DateFilterOption.thisMonth:
        startDate = DateTime(today.year, today.month, 1);
        // Last day of current month
        final nextMonth = today.month < 12
            ? DateTime(today.year, today.month + 1, 1)
            : DateTime(today.year + 1, 1, 1);
        endDate = nextMonth.subtract(const Duration(days: 1));
        endDate = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
        break;
      case DateFilterOption.lastMonth:
        final lastMonth = today.month == 1
            ? DateTime(today.year - 1, 12, 1)
            : DateTime(today.year, today.month - 1, 1);
        startDate = lastMonth;
        // Last day of last month
        final thisMonthFirst = DateTime(today.year, today.month, 1);
        endDate = thisMonthFirst.subtract(const Duration(days: 1));
        endDate = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
        break;
      case DateFilterOption.thisYear:
        startDate = DateTime(today.year, 1, 1);
        endDate = DateTime(today.year, 12, 31, 23, 59, 59);
        break;
      case DateFilterOption.lastYear:
        startDate = DateTime(today.year - 1, 1, 1);
        endDate = DateTime(today.year - 1, 12, 31, 23, 59, 59);
        break;
      case DateFilterOption.custom:
        // Show date range picker for custom range
        final initialDateRange = DateTimeRange(
          start: widget.startDate ?? today.subtract(const Duration(days: 30)),
          end: widget.endDate ?? today,
        );

        final pickedDateRange = await showDateRangePicker(
          context: context,
          initialDateRange: initialDateRange,
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 365)),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(
                  primary: const Color(0xFF2E7D32),
                  onPrimary: Colors.white,
                  surface: Colors.white,
                  onSurface: Colors.black,
                ),
              ),
              child: child!,
            );
          },
        );

        if (pickedDateRange != null) {
          startDate = pickedDateRange.start;
          // Set end date to end of day
          endDate = DateTime(
            pickedDateRange.end.year,
            pickedDateRange.end.month,
            pickedDateRange.end.day,
            23, 59, 59
          );
        } else {
          // If user cancels, revert to previous selection
          return;
        }
        break;
    }

    widget.onDateRangeChanged(startDate, endDate, option);
  }

  /// Format date range for display
  String _getDisplayText() {
    if (widget.startDate == null || widget.endDate == null) {
      return 'All Time';
    }

    if (_selectedOption != DateFilterOption.custom) {
      return _selectedOption.label;
    }

    // Use shorter date format for custom range to save space
    final formatter = DateFormat('MM/dd/yy');
    return '${formatter.format(widget.startDate!)} - ${formatter.format(widget.endDate!)}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: widget.borderColor ?? Colors.grey.shade300),
      ),
      child: PopupMenuButton<DateFilterOption>(
        tooltip: 'Filter by date',
        initialValue: _selectedOption,
        onSelected: _applyDateFilter,
        itemBuilder: (context) => DateFilterOption.values.map((option) {
          return PopupMenuItem<DateFilterOption>(
            value: option,
            child: Row(
              children: [
                Icon(
                  option.icon,
                  color: option == _selectedOption
                      ? Colors.green.shade600
                      : widget.iconColor ?? Colors.grey.shade700,
                  size: 18,
                ),
                const SizedBox(width: 12),
                Text(
                  option.label,
                  style: TextStyle(
                    color: option == _selectedOption
                        ? Colors.green.shade600
                        : widget.textColor ?? Colors.black,
                    fontWeight: option == _selectedOption
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
                const Spacer(),
                if (option == _selectedOption)
                  Icon(Icons.check, color: Colors.green.shade600, size: 18)
              ],
            ),
          );
        }).toList(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _selectedOption.icon,
                size: 14,
                color: widget.iconColor ?? Colors.grey.shade700,
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  _getDisplayText(),
                  style: TextStyle(
                    fontSize: 12,
                    color: widget.textColor ?? Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                Icons.arrow_drop_down,
                size: 16,
                color: widget.iconColor ?? Colors.grey.shade700,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
