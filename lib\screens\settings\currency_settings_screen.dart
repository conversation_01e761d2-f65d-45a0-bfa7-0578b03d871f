import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/currency.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';
import 'package:tubewell_water_billing/widgets/app_bar_widget.dart';

class CurrencySettingsScreen extends StatefulWidget {
  const CurrencySettingsScreen({super.key});

  @override
  State<CurrencySettingsScreen> createState() => _CurrencySettingsScreenState();
}

class _CurrencySettingsScreenState extends State<CurrencySettingsScreen> {
  // Currency settings
  Currency _selectedCurrency = Currencies.pakistaniRupee;
  final _availableCurrencies = Currencies.all;
  bool _isLoading = true;

  // Default hourly rate
  final TextEditingController _hourlyRateController = TextEditingController(text: '900');

  @override
  void initState() {
    super.initState();
    _loadCurrencySettings();
  }

  @override
  void dispose() {
    _hourlyRateController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrencySettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Force re-initialization of currency service to ensure latest settings
      await CurrencyService.initialize();

      // Get current currency
      final currentCurrency = CurrencyService.currentCurrency;

      // Get default hourly rate
      final defaultHourlyRate = await CurrencyService.getDefaultHourlyRate();
      _hourlyRateController.text = defaultHourlyRate.toString();

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _selectedCurrency = currentCurrency;
        _isLoading = false;
      });
    } catch (e) {
      // Check if widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading currency settings: $e'),
          backgroundColor: Colors.red,
        ),
      );

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveCurrency(Currency currency) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Parse hourly rate
      final hourlyRate = double.tryParse(_hourlyRateController.text) ?? 900.0;

      // Set the currency which will also notify listeners
      await CurrencyService.setCurrency(currency);

      // Save the hourly rate
      await CurrencyService.saveDefaultHourlyRate(hourlyRate);

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _selectedCurrency = currency;
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Settings saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving settings: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TubewellAppBar(
        title: 'Currency Settings',
        showPdfOption: false,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                _buildCurrencySettings(),
                const SizedBox(height: 20),
                _buildHourlyRateSettings(),
                const SizedBox(height: 20),
                _buildCurrencyPreview(),
                const SizedBox(height: 20),
                _buildSaveButton(),
              ],
            ),
    );
  }

  Widget _buildCurrencySettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Currency',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Select your preferred currency that will be used throughout the app.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Currency>(
              value: _selectedCurrency,
              decoration: const InputDecoration(
                labelText: 'Currency',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              items: _availableCurrencies.map((currency) {
                return DropdownMenuItem<Currency>(
                  value: currency,
                  child: Row(
                    children: [
                      Text(currency.symbol,
                          style: const TextStyle(fontSize: 18)),
                      const SizedBox(width: 12),
                      Text(currency.name),
                      const SizedBox(width: 8),
                      Text('(${currency.code})',
                          style: TextStyle(
                              color: Colors.grey.shade600, fontSize: 12)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (currency) {
                if (currency != null) {
                  setState(() {
                    _selectedCurrency = currency;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHourlyRateSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Default Hourly Rate',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Set the default hourly rate for new transactions. This rate will be auto-populated in the transaction form.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _hourlyRateController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Hourly Rate (${_selectedCurrency.symbol})',
                hintText: 'Enter your default charging rate per hour',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade400),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.green.shade700, width: 2),
                ),
                filled: true,
                fillColor: Colors.green.shade50,
                prefixIcon: Icon(Icons.attach_money, color: Colors.green.shade700),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                helperText: 'This rate will be pre-filled when creating new transactions',
              ),
              onChanged: (value) {
                // Update preview
                if (mounted) {
                  setState(() {});
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an hourly rate';
                }
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencyPreview() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Currency Preview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is how the selected currency will be displayed throughout the app.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Card(
              color: Colors.grey.shade100,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Symbol: ${_selectedCurrency.symbol}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Name: ${_selectedCurrency.name}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Code: ${_selectedCurrency.code}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Country Code: ${_selectedCurrency.countryCode} (for WhatsApp messages)',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Default Hourly Rate: ${_selectedCurrency.symbol} ${_hourlyRateController.text}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Example Amounts:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Small: ${CurrencyService.formatCurrency(100, decimalPlaces: 2)}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Medium: ${CurrencyService.formatCurrency(1000, decimalPlaces: 2)}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Large: ${CurrencyService.formatCurrency(10000, decimalPlaces: 2)}',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton(
      onPressed: () async {
        await _saveCurrency(_selectedCurrency);
        if (mounted) {
          Navigator.pop(context, true); // Return true to trigger refresh
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: const Text(
        'Save Currency Settings',
        style: TextStyle(fontSize: 16),
      ),
    );
  }
}
