import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import 'package:tubewell_water_billing/models/account.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/settings_service.dart';

class AccountService {
  static const String _accountsKey = 'accounts';
  static const String _currentAccountIdKey = 'currentAccountId';

  static Account? _currentAccount;
  static List<Account> _accounts = [];
  static bool _isInitialized = false;

  // Get the current account
  static Account? get currentAccount => _currentAccount;

  // Get all accounts
  static List<Account> get accounts => List.unmodifiable(_accounts);

  // Initialize the account service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    final prefs = await SharedPreferences.getInstance();

    // Load accounts
    final accountsJson = prefs.getStringList(_accountsKey);
    if (accountsJson != null) {
      try {
        _accounts = accountsJson
            .map((json) {
              try {
                final map = jsonDecode(json);
                if (map is Map<String, dynamic>) {
                  return Account.fromMap(map);
                } else {
                  // Invalid account data format
                  return null;
                }
              } catch (e) {
                // Error decoding JSON for account
                return null;
              }
            })
            .where((account) => account != null)
            .cast<Account>()
            .toList();
      } catch (e) {
        // Error loading accounts, use empty list
        _accounts = [];
      }
    }

    // If no accounts exist, create a default account
    if (_accounts.isEmpty) {
      final defaultAccount = Account(name: 'Default Account');
      _accounts.add(defaultAccount);
      await _saveAccounts();
      // Store account name in settings table for backup purposes
      await _storeAccountNameInSettings(defaultAccount);
    }

    // Load current account ID
    final currentAccountId = prefs.getString(_currentAccountIdKey);
    if (currentAccountId != null) {
      _currentAccount = _accounts.firstWhere(
        (account) => account.id == currentAccountId,
        orElse: () => _accounts.first,
      );
    } else {
      _currentAccount = _accounts.first;
      await _saveCurrentAccountId();
    }

    // Update last accessed time for current account
    _currentAccount!.updateLastAccessed();
    await _saveAccounts();

    _isInitialized = true;
  }

  // Save accounts to shared preferences
  static Future<void> _saveAccounts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accountsJson = _accounts
          .map((account) {
            try {
              final map = account.toMap();
              return jsonEncode(map);
            } catch (e) {
              // Error encoding account to JSON
              return null;
            }
          })
          .where((json) => json != null)
          .cast<String>()
          .toList();

      await prefs.setStringList(_accountsKey, accountsJson);
      // Accounts saved successfully
    } catch (e) {
      // Error saving accounts to SharedPreferences
    }
  }

  // Save current account ID to shared preferences
  static Future<void> _saveCurrentAccountId() async {
    if (_currentAccount == null) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentAccountIdKey, _currentAccount!.id);
  }

  // Add a new account
  static Future<Account> addAccount(String name, {String? description}) async {
    await _ensureInitialized();

    // Validate input
    if (name.isEmpty) {
      throw Exception('Account name cannot be empty');
    }

    // Sanitize input - remove any characters that might cause JSON issues
    final sanitizedName = name.trim();
    final sanitizedDescription = description?.trim();

    // Check if account with this name already exists
    if (_accounts.any((account) => account.name == sanitizedName)) {
      throw Exception('Account with this name already exists');
    }

    try {
      final account = Account(
        name: sanitizedName,
        description: sanitizedDescription,
      );

      _accounts.add(account);
      await _saveAccounts();

      // Store account name in settings table for backup purposes
      await _storeAccountNameInSettings(account);

      // Successfully added account
      return account;
    } catch (e) {
      // Error adding account
      throw Exception('Failed to add account: $e');
    }
  }

  // Update an account
  static Future<void> updateAccount(Account account) async {
    await _ensureInitialized();

    final index = _accounts.indexWhere((a) => a.id == account.id);
    if (index == -1) {
      throw Exception('Account not found');
    }

    _accounts[index] = account;
    await _saveAccounts();

    // Store updated account name in settings table for backup purposes
    await _storeAccountNameInSettings(account);

    // If this is the current account, update the reference
    if (_currentAccount?.id == account.id) {
      _currentAccount = account;
    }
  }

  // Delete an account
  static Future<void> deleteAccount(String accountId) async {
    await _ensureInitialized();

    // Cannot delete the only account
    if (_accounts.length <= 1) {
      throw Exception('Cannot delete the only account');
    }

    // Cannot delete the current account
    if (_currentAccount?.id == accountId) {
      throw Exception(
          'Cannot delete the current account. Switch to another account first.');
    }

    _accounts.removeWhere((account) => account.id == accountId);
    await _saveAccounts();
  }

  // Switch to another account
  static Future<void> switchAccount(String accountId) async {
    final logger = Logger();
    logger.i('Switching to account: $accountId');

    await _ensureInitialized();

    final account = _accounts.firstWhere(
      (account) => account.id == accountId,
      orElse: () => throw Exception('Account not found'),
    );

    // If already on this account, do nothing
    if (_currentAccount?.id == accountId) {
      logger.i('Already on account: $accountId, no switch needed');
      return;
    }

    try {
      // Close current database connection
      logger.i('Closing current database connection');
      await DatabaseService.closeDatabase();

      // Add a small delay to ensure closure is complete
      await Future.delayed(const Duration(milliseconds: 100));

      // Update current account
      _currentAccount = account;
      _currentAccount!.updateLastAccessed();

      // Save changes
      await _saveCurrentAccountId();
      await _saveAccounts();

      // Reinitialize database with new account context
      logger.i('Initializing database for new account: $accountId');
      await DatabaseService.initialize(accountId: accountId);

      logger.i('Account switch completed successfully');
    } catch (e) {
      logger.e('Error switching account: $e');

      // If an error occurs, try to recover by reinitializing the database
      try {
        logger.i('Attempting to recover from account switch error');
        await DatabaseService.closeDatabase();
        await Future.delayed(const Duration(milliseconds: 100));
        await DatabaseService.initialize(accountId: accountId);
      } catch (recoveryError) {
        logger.e('Recovery attempt failed: $recoveryError');
        // If recovery fails, rethrow the original error
        throw Exception('Failed to switch account: $e');
      }
    }
  }

  // Force reinitialization (useful after restore operations)
  static Future<void> forceReinitialize() async {
    _isInitialized = false;
    _accounts.clear();
    _currentAccount = null;
    await initialize();
  }

  // Set a single authoritative account and clear all others (used during restore)
  static Future<void> setActiveAccountAndClearOthers(String accountId, String accountName) async {
    try {
      debugPrint('Setting authoritative account: $accountName ($accountId)');

      final prefs = await SharedPreferences.getInstance();
      debugPrint('SharedPreferences obtained');

      // Clear all existing accounts from internal state
      _accounts.clear();
      _currentAccount = null;
      debugPrint('Cleared internal account state');

      // Clear all existing accounts from SharedPreferences
      await prefs.remove(_accountsKey);
      await prefs.remove(_currentAccountIdKey);
      debugPrint('Cleared SharedPreferences account data');

      // Create the new authoritative account
      final authoritativeAccount = Account(
        id: accountId,
        name: accountName,
        createdAt: DateTime.now(),
        lastAccessed: DateTime.now(),
      );
      debugPrint('Created authoritative account object');

      // Set this as the only account
      _accounts = [authoritativeAccount];
      _currentAccount = authoritativeAccount;
      debugPrint('Set account as current');

      // Save to SharedPreferences
      await _saveAccounts();
      debugPrint('Saved accounts to SharedPreferences');

      await _saveCurrentAccountId();
      debugPrint('Saved current account ID');

      // Mark as initialized
      _isInitialized = true;
      debugPrint('Marked AccountService as initialized');

      // Store account name in settings table for backup purposes (skip during restore to avoid DB issues)
      // This will be done later when the database is properly initialized
      debugPrint('Skipping account name storage in settings during restore');

      debugPrint('Set authoritative account: $accountName ($accountId)');
    } catch (e) {
      debugPrint('Error setting authoritative account: $e');
      throw Exception('Failed to set authoritative account: $e');
    }
  }

  // Ensure the service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // Get the database name for an account
  static String getDatabaseName(String accountId) {
    return 'account_$accountId.db';
  }

  // Store account name in settings table for backup purposes
  static Future<void> _storeAccountNameInSettings(Account account) async {
    try {
      await SettingsService.saveAccountName(account.name, accountId: account.id);
    } catch (e) {
      // Don't throw - this is not critical for account operations
      debugPrint('Error storing account name in settings: $e');
    }
  }

  // Store current account name in settings (safe to call after database is initialized)
  static Future<void> storeCurrentAccountNameInSettings() async {
    try {
      if (_currentAccount != null) {
        await _storeAccountNameInSettings(_currentAccount!);
        debugPrint('Stored current account name in settings: ${_currentAccount!.name}');
      }
    } catch (e) {
      debugPrint('Error storing current account name in settings: $e');
    }
  }
}
