import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/forms/customer_form_screen.dart';
import 'package:tubewell_water_billing/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing/forms/expense_form_screen.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';

enum FabType {
  transaction,
  customer,
  payment,
  expense,
}

class UniversalFab extends StatelessWidget {
  final FabType type;
  final VoidCallback? onPressed;
  final Bill? selectedBill;
  final Customer? selectedCustomer;
  final Function(bool)? onResult;
  final String? heroTag;
  final String? label;

  const UniversalFab({
    super.key,
    required this.type,
    this.onPressed,
    this.selectedBill,
    this.selectedCustomer,
    this.onResult,
    this.heroTag,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    // Custom handling for customer FAB to show menu
    if (type == FabType.customer) {
      return FloatingActionButton(
        heroTag: heroTag,
        onPressed: onPressed ?? () => _showCustomerAddOptions(context),
        backgroundColor: const Color(0xFF2E7D32),
        child: Icon(_getIconData()),
      );
    }

    // Extended FAB with label if provided
    if (label != null) {
      return FloatingActionButton.extended(
        heroTag: heroTag,
        onPressed: onPressed ?? () => _handleFabPressed(context),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        elevation: 4,
        icon: Icon(_getIconData()),
        label: Text(label!),
      );
    }

    // Default FAB for other types
    return FloatingActionButton(
      heroTag: heroTag,
      onPressed: onPressed ?? () => _handleFabPressed(context),
      backgroundColor: const Color(0xFF2E7D32),
      child: Icon(_getIconData()),
    );
  }

  IconData _getIconData() {
    switch (type) {
      case FabType.transaction:
        return Icons.add_chart;
      case FabType.customer:
        return Icons.person_add;
      case FabType.payment:
        return Icons.payment;
      case FabType.expense:
        return Icons.money_off;
    }
  }

  void _showCustomerAddOptions(BuildContext context) {
    // Since we removed the contact import feature, directly navigate to add customer manually
    _handleAddCustomerManually(context);
  }

  Future<void> _handleAddCustomerManually(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomerFormScreen(),
      ),
    );
    if (result == true && onResult != null) {
      onResult!(true);
    }
  }



  Future<void> _handleFabPressed(BuildContext context) async {
    switch (type) {
      case FabType.transaction:
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TransactionFormScreen(
              selectedCustomer: selectedCustomer,
            ),
          ),
        );
        if (result == true && onResult != null) {
          onResult!(true);
        }
        break;
      case FabType.customer:
        // Now handled by the menu
        break;
      case FabType.payment:
        // Payment form requires both a bill and customer
        // This will typically be handled by the onPressed callback
        // since we need to select a bill first
        if (onPressed == null && onResult != null) {
          onResult!(false);
        }
        break;
      case FabType.expense:
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ExpenseFormScreen(),
          ),
        );
        // Always notify about result to refresh data regardless of form result
        if (onResult != null) {
          onResult!(true);
        }
        break;
    }
  }
}
