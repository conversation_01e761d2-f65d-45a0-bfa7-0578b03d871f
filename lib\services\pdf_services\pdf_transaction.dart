import 'dart:io';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_utility_service.dart';

/// Service for generating transaction list PDFs
class PdfTransactionService {
  /// Generate a transactions list PDF for multiple bills
  static Future<File> generateTransactionsListPdf({
    required List<Bill> bills,
    required Map<int, Customer> customersMap,
    Map<String, num>? summary,
    Map<String, dynamic>? filters,
    PdfSettings? settings,
  }) async {
    try {
      // Use provided settings or create default modern settings
      final pdfSettings = settings ?? PdfSettings.modern().copyWith(
        primaryColor: PdfColor.fromHex('#2E7D32'), // Green
        accentColor: PdfColor.fromHex('#1565C0'), // Blue
        additionalSettings: {
          'footerText': 'Transactions Report',
        },
      );

      // Define colors from settings
      final primaryColor = pdfSettings.primaryColor;
      final accentColor = pdfSettings.accentColor;
      final textColor = pdfSettings.textColor;
      final lightGreen = PdfBaseService.lightGreen;
      final lightBlue = PdfBaseService.lightBlue;
      final lightRed = PdfBaseService.lightRed;

      // Load fonts
      await PdfBaseService.loadFonts();
      final regularFont = PdfBaseService.regularFont;
      final boldFont = PdfBaseService.boldFont;
      final italicFont = PdfBaseService.italicFont;

      // Create a PDF document with template
      final pdf = await PdfTemplateService.createPdfWithTemplate(
        title: 'TRANSACTIONS REPORT',
        subtitle: 'Transaction Records',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        watermarkText: pdfSettings.showWatermark ? (pdfSettings.watermarkText ?? 'COPY') : null,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      // Sort bills by date (newest first)
      bills.sort((a, b) => b.billDate.compareTo(a.billDate));

      // Calculate totals if not provided
      Map<String, num> summaryData = summary ?? {
        'totalAmount': 0.0,
        'totalPaid': 0.0,
        'totalOutstanding': 0.0,
        'totalBills': bills.length,
        'paidBills': 0,
        'partialBills': 0,
        'unpaidBills': 0,
      };

      if (summary == null) {
        for (var bill in bills) {
          summaryData['totalAmount'] = (summaryData['totalAmount'] ?? 0) + bill.amount;

          if (bill.isPaid) {
            summaryData['totalPaid'] = (summaryData['totalPaid'] ?? 0) + bill.amount;
            summaryData['paidBills'] = (summaryData['paidBills'] ?? 0) + 1;
          } else if (bill.isPartiallyPaid) {
            summaryData['totalPaid'] = (summaryData['totalPaid'] ?? 0) + (bill.partialAmount ?? 0);
            summaryData['totalOutstanding'] = (summaryData['totalOutstanding'] ?? 0) + bill.outstandingAmount;
            summaryData['partialBills'] = (summaryData['partialBills'] ?? 0) + 1;
          } else {
            summaryData['totalOutstanding'] = (summaryData['totalOutstanding'] ?? 0) + bill.amount;
            summaryData['unpaidBills'] = (summaryData['unpaidBills'] ?? 0) + 1;
          }
        }
      }

      // Create content widgets
      final List<pw.Widget> contentWidgets = [
        // Date range if provided
        if (filters != null && (filters['startDate'] != null || filters['endDate'] != null))
          pw.Text(
              'Period: ${filters['startDate'] != null ? DateFormat('dd/MM/yyyy').format(filters['startDate']) : 'Start'} - ${filters['endDate'] != null ? DateFormat('dd/MM/yyyy').format(filters['endDate']) : 'End'}',
              style: pw.TextStyle(font: regularFont, fontSize: 10)),
        pw.SizedBox(height: 20),

            // Report Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: lightGreen,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('TRANSACTIONS SUMMARY',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 14,
                        color: primaryColor,
                      )),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Bills:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('${summaryData['totalBills'] ?? 0}',
                                style: pw.TextStyle(font: boldFont, fontSize: 16)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Amount:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(summaryData['totalAmount'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: boldFont,
                                    fontSize: 16,
                                    color: primaryColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Paid Amount:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(summaryData['totalPaid'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: regularFont,
                                    fontSize: 14,
                                    color: primaryColor)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Outstanding:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(summaryData['totalOutstanding'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: regularFont,
                                    fontSize: 14,
                                    color: PdfColor.fromHex('#C62828'))),
                          ],
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Row(
                          children: [
                            pw.Container(
                              width: 12,
                              height: 12,
                              decoration: pw.BoxDecoration(
                                color: primaryColor,
                                shape: pw.BoxShape.circle,
                              ),
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text('Paid: ${summaryData['paidBills'] ?? 0}',
                                style: pw.TextStyle(font: regularFont, fontSize: 10)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Row(
                          children: [
                            pw.Container(
                              width: 12,
                              height: 12,
                              decoration: pw.BoxDecoration(
                                color: accentColor,
                                shape: pw.BoxShape.circle,
                              ),
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text('Partial: ${summaryData['partialBills'] ?? 0}',
                                style: pw.TextStyle(font: regularFont, fontSize: 10)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Row(
                          children: [
                            pw.Container(
                              width: 12,
                              height: 12,
                              decoration: pw.BoxDecoration(
                                color: PdfColor.fromHex('#C62828'),
                                shape: pw.BoxShape.circle,
                              ),
                            ),
                            pw.SizedBox(width: 5),
                            pw.Text('Unpaid: ${summaryData['unpaidBills'] ?? 0}',
                                style: pw.TextStyle(font: regularFont, fontSize: 10)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (filters != null && filters['isPaid'] != null) ...[
                    pw.SizedBox(height: 10),
                    pw.Text('Status Filter: ${filters['isPaid']}',
                        style: pw.TextStyle(font: boldFont, fontSize: 10)),
                  ],
                  if (filters != null && filters['customerId'] != null) ...[
                    pw.SizedBox(height: 5),
                    pw.Text('Customer Filter: ${customersMap[filters['customerId']]?.name ?? 'Unknown'}',
                        style: pw.TextStyle(font: boldFont, fontSize: 10)),
                  ],
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // Transactions Table
            pw.Text('TRANSACTION DETAILS',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 14,
                  color: primaryColor,
                )),
            pw.Divider(color: primaryColor),

            // Check if bills exist
            if (bills.isEmpty)
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: lightBlue,
                  borderRadius:
                      const pw.BorderRadius.all(pw.Radius.circular(5)),
                ),
                child: pw.Center(
                  child: pw.Text('No transactions found for the selected criteria',
                      style: pw.TextStyle(font: italicFont, fontSize: 12)),
                ),
              )
            else
              pw.Table(
                border: pw.TableBorder.all(
                  color: PdfColor.fromHex('#CCCCCC'),
                  width: 0.5,
                ),
                columnWidths: {
                  0: const pw.FixedColumnWidth(30), // ID
                  1: const pw.FlexColumnWidth(2), // Customer
                  2: const pw.FixedColumnWidth(70), // Date
                  3: const pw.FixedColumnWidth(60), // Duration
                  4: const pw.FixedColumnWidth(60), // Amount
                  5: const pw.FixedColumnWidth(50), // Discount
                  6: const pw.FixedColumnWidth(60), // Paid
                  7: const pw.FixedColumnWidth(50), // Status
                },
                children: [
                  // Table header
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      color: lightGreen,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('ID',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Customer',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Date',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Duration',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Amount',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Discount',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Paid',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Status',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                    ],
                  ),
                  // Table rows
                  ...bills.map((bill) {
                    final customer = customersMap[bill.customerId];
                    final paidAmount = bill.isPaid ? bill.amount : (bill.partialAmount ?? 0);

                    // Determine status color
                    final PdfColor statusColor = bill.isPaid
                        ? primaryColor
                        : (bill.isPartiallyPaid ? accentColor : PdfColor.fromHex('#C62828'));

                    // Determine status text
                    final String billStatus = bill.isPaid
                        ? 'PAID'
                        : (bill.isPartiallyPaid ? 'PARTIAL' : 'UNPAID');

                    // Determine discount text
                    final String discountText = (bill.discountAmount != null && bill.discountAmount! > 0)
                        ? 'Rs. ${bill.discountAmount!.toStringAsFixed(2)}'
                        : ((bill.discountTime != null && bill.discountTime! > 0)
                            ? '${bill.discountTime!.round()}m'
                            : 'None');

                    return pw.TableRow(
                      decoration: pw.BoxDecoration(
                        color: bill.isPaid
                            ? PdfBaseService.colorWithOpacity(lightGreen, 0.2)
                            : (bill.isPartiallyPaid
                                ? PdfBaseService.colorWithOpacity(lightBlue, 0.2)
                                : PdfBaseService.colorWithOpacity(lightRed, 0.2)),
                      ),
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text('#${bill.id}',
                              style: pw.TextStyle(
                                  font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(customer?.name ?? 'Unknown',
                                  style: pw.TextStyle(
                                      font: boldFont, fontSize: 9)),
                              if (bill.remarks != null && bill.remarks!.isNotEmpty)
                                pw.Text(bill.remarks!,
                                    style: pw.TextStyle(
                                        font: italicFont, fontSize: 8),
                                    maxLines: 1),
                            ],
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              DateFormat('dd/MM/yyyy')
                                  .format(bill.billDate),
                              style: pw.TextStyle(
                                  font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              '${bill.durationHoursWhole}h ${bill.durationMinutes}m',
                              style: pw.TextStyle(
                                  font: regularFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              'Rs. ${bill.amount.toStringAsFixed(2)}',
                              style: pw.TextStyle(
                                  font: boldFont, fontSize: 9)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              discountText,
                              style: pw.TextStyle(
                                  font: regularFont,
                                  fontSize: 8,
                                  color: discountText != 'None' ? accentColor : null)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                              'Rs. ${paidAmount.toStringAsFixed(2)}',
                              style: pw.TextStyle(
                                  font: boldFont, fontSize: 9,
                                  color: paidAmount > 0 ? primaryColor : null)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(billStatus,
                              style: pw.TextStyle(
                                  font: boldFont,
                                  fontSize: 9,
                                  color: statusColor)),
                        ),
                      ],
                    );
                  }),
                ],
              ),

      ];

      // Add page with template using settings
      await PdfTemplateService.addPageWithTemplate(
        pdf: pdf,
        contentWidgets: PdfTemplateService.createContentSection(
          contentStyle: pdfSettings.contentStyle,
          contentWidgets: contentWidgets,
          primaryColor: primaryColor,
          accentColor: accentColor,
          textColor: textColor,
        ),
        title: 'TRANSACTIONS REPORT',
        subtitle: 'Transaction Records',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        contentStyle: pdfSettings.contentStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        headerData: {
          ...pdfSettings.getHeaderData(),
          'period': filters != null && (filters['startDate'] != null || filters['endDate'] != null)
              ? 'Period: ${filters['startDate'] != null ? DateFormat('dd/MM/yyyy').format(filters['startDate']) : 'Start'} - ${filters['endDate'] != null ? DateFormat('dd/MM/yyyy').format(filters['endDate']) : 'End'}'
              : null,
        },
        footerData: pdfSettings.getFooterData(),
      );

      // Save the PDF file
      final fileName = 'transactions_list_${DateFormat('yyyyMMdd').format(DateTime.now())}.pdf';
      return await PdfUtilityService.savePdfToTemp(pdf, fileName);
    } catch (e) {
      rethrow;
    }
  }
}
