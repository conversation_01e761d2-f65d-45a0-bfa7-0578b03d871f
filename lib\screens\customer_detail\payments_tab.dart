import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/payment_service.dart';
import 'package:tubewell_water_billing/widgets/empty_state_widget.dart';
import 'package:tubewell_water_billing/widgets/info_chips.dart';
import 'package:tubewell_water_billing/screens/payments_screen.dart';
import 'package:tubewell_water_billing/forms/auto_payment_form_screen.dart';

import 'package:intl/intl.dart';
import 'dart:async';

// Sort options for payments
enum PaymentSortOption {
  dateNewest(label: 'Date (Newest first)', icon: Icons.arrow_downward),
  dateOldest(label: 'Date (Oldest first)', icon: Icons.arrow_upward),
  amountHighest(label: 'Amount (Highest first)', icon: Icons.arrow_downward),
  amountLowest(label: 'Amount (Lowest first)', icon: Icons.arrow_upward),
  methodAZ(label: 'Payment Method (A-Z)', icon: Icons.arrow_downward),
  methodZA(label: 'Payment Method (Z-A)', icon: Icons.arrow_upward);

  final String label;
  final IconData icon;

  const PaymentSortOption({
    required this.label,
    required this.icon,
  });
}

class CustomerPaymentsTab extends StatefulWidget {
  final Customer customer;
  final VoidCallback onDataChanged;

  const CustomerPaymentsTab({
    super.key,
    required this.customer,
    required this.onDataChanged,
  });

  @override
  State<CustomerPaymentsTab> createState() => CustomerPaymentsTabState();
}

class CustomerPaymentsTabState extends State<CustomerPaymentsTab>
    with AutomaticKeepAliveClientMixin {
  List<Payment> _payments = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();

  // Search state
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // Sort state
  PaymentSortOption _currentSortOption = PaymentSortOption.dateNewest;
  bool _isSortActive = false;

  // Debounce search to reduce unnecessary rebuilds
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // For pull-to-refresh coordination
  Completer<void>? _refreshCompleter;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadPayments();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDuration, () {
      final query = _searchController.text.trim().toLowerCase();
      if (_searchQuery != query) {
        setState(() {
          _searchQuery = query;
        });
        _loadPayments();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Only refresh if not already loading
    if (!_isLoading && !_isLoadingMore) {
      _loadPayments();
    }
  }

  // Scroll listener for infinite scrolling
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMorePayments();
      }
    }
  }

  // Public method to force a refresh from parent
  void refreshData() {
    if (mounted && !_isLoading) {
      _loadPayments();
    }
  }

  // Used by pull-to-refresh
  Future<void> _handleRefresh() {
    _refreshCompleter = Completer<void>();
    refreshData();
    return _refreshCompleter!.future;
  }

  Future<void> _loadPayments() async {
    if (!mounted) return;

    // Reset pagination
    _page = 0;
    _hasMoreData = true;

    setState(() {
      _isLoading = true;
    });

    try {
      final payments = await DatabaseService.getPaymentsByCustomer(
        widget.customer.id,
        offset: _page * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      _processLoadedPayments(payments, isFirstPage: true);
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading payments: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Complete the refresh indicator if it's active
      _completeRefresh();
    }
  }

  Future<void> _loadMorePayments() async {
    if (!mounted || !_hasMoreData || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _page++;
      final payments = await DatabaseService.getPaymentsByCustomer(
        widget.customer.id,
        offset: _page * _pageSize,
        limit: _pageSize,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      _processLoadedPayments(payments, isFirstPage: false);
    } catch (e) {
      if (mounted) {
        // Revert page increment on error
        _page--;

        setState(() {
          _isLoadingMore = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading more payments: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _processLoadedPayments(List<Payment> payments,
      {required bool isFirstPage}) {
    if (!mounted) return;

    // Check if we've reached the end
    _hasMoreData = payments.length >= _pageSize;

    // Update the payments list
    if (isFirstPage) {
      _payments = payments;
    } else {
      _payments.addAll(payments);
    }

    // Apply sorting
    _sortPayments(_payments);

    setState(() {
      _isLoading = false;
      _isLoadingMore = false;
    });

    // Notify parent to refresh summary
    widget.onDataChanged();

    // Complete the refresh indicator if it's active
    _completeRefresh();
  }

  void _completeRefresh() {
    if (_refreshCompleter?.isCompleted == false) {
      _refreshCompleter?.complete();
    }
  }

  // Sort payments based on current sort option
  void _sortPayments(List<Payment> payments) {
    switch (_currentSortOption) {
      case PaymentSortOption.dateNewest:
        payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
        break;
      case PaymentSortOption.dateOldest:
        payments.sort((a, b) => a.paymentDate.compareTo(b.paymentDate));
        break;
      case PaymentSortOption.amountHighest:
        payments.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case PaymentSortOption.amountLowest:
        payments.sort((a, b) => a.amount.compareTo(b.amount));
        break;
      case PaymentSortOption.methodAZ:
        payments.sort((a, b) {
          final methodA = a.paymentMethod ?? '';
          final methodB = b.paymentMethod ?? '';
          final comparison = methodA.compareTo(methodB);
          return comparison != 0 ? comparison : b.paymentDate.compareTo(a.paymentDate);
        });
        break;
      case PaymentSortOption.methodZA:
        payments.sort((a, b) {
          final methodA = a.paymentMethod ?? '';
          final methodB = b.paymentMethod ?? '';
          final comparison = methodB.compareTo(methodA);
          return comparison != 0 ? comparison : b.paymentDate.compareTo(a.paymentDate);
        });
        break;
    }
  }

  // Handle sort option change
  void _onSortChanged(PaymentSortOption option) {
    setState(() {
      _currentSortOption = option;
      _isSortActive = option != PaymentSortOption.dateNewest;
    });

    // Re-process current payments with new sort
    _processLoadedPayments(_payments, isFirstPage: true);
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
    });
    _loadPayments();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    if (_isLoading && _payments.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_payments.isEmpty) {
      return Column(
        children: [
          // Search and filter bar
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search payments...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                  },
                                )
                              : null,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Sort button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: IconButton(
                        icon: Stack(
                          alignment: Alignment.center,
                          children: [
                            const Icon(Icons.sort),
                            if (_isSortActive)
                              Positioned(
                                right: 0,
                                bottom: 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.purple,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        tooltip: 'Sort: ${_currentSortOption.label}',
                        onPressed: _showSortDialog,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: EmptyStateWidget(
              icon: _searchQuery.isNotEmpty
                  ? Icons.search_off
                  : Icons.payment,
              title: _searchQuery.isNotEmpty
                  ? 'No Results Found'
                  : 'No Payments',
              message: _searchQuery.isNotEmpty
                  ? 'Try different search terms'
                  : 'This customer has no payments yet.',
              buttonText: _searchQuery.isNotEmpty
                  ? 'Clear Search'
                  : 'Add Payment',
              onButtonPressed: () {
                if (_searchQuery.isNotEmpty) {
                  _clearSearch();
                } else {
                  // Navigate directly to auto payment form
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AutoPaymentFormScreen(
                        customer: widget.customer,
                      ),
                    ),
                  ).then((result) {
                    if (result == true) {
                      refreshData();
                    }
                  });
                }
              },
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        // Search and filter bar
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search payments...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                },
                              )
                            : null,
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Sort button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: IconButton(
                      icon: Stack(
                        alignment: Alignment.center,
                        children: [
                          const Icon(Icons.sort),
                          if (_isSortActive)
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.purple,
                                ),
                              ),
                            ),
                        ],
                      ),
                      tooltip: 'Sort: ${_currentSortOption.label}',
                      onPressed: _showSortDialog,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _handleRefresh,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // Payment list as a sliver
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      // Show loading indicator at the end
                      if (index == _payments.length) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Center(
                            child: _isLoadingMore
                                ? const CircularProgressIndicator(strokeWidth: 2)
                                : TextButton(
                                    onPressed: _loadMorePayments,
                                    child: Text('Load More',
                                        style:
                                            TextStyle(color: Colors.teal.shade700)),
                                  ),
                          ),
                        );
                      }

                      final payment = _payments[index];
                      return _buildPaymentCard(payment, context);
                    },
                    childCount: _payments.length + (_hasMoreData ? 1 : 0),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Show sort options dialog
  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Payments'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: PaymentSortOption.values.map((option) {
            final isSelected = option == _currentSortOption;
            return ListTile(
              leading: Icon(
                option.icon,
                color: isSelected ? Colors.purple : null,
              ),
              title: Text(option.label),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Colors.purple)
                  : null,
              selected: isSelected,
              selectedColor: Colors.purple,
              onTap: () {
                Navigator.pop(context);
                _onSortChanged(option);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentCard(Payment payment, BuildContext context) {
    final formattedDate = DateFormat('dd MMM yyyy').format(payment.paymentDate);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: payment.billId > 0
                ? Colors.green.shade200
                : Colors.blue.shade200,
            width: 1.5,
          ),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PaymentsScreen(
                  selectedCustomer: widget.customer,
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row 1: Payment ID and Bill allocation status
                Row(
                  children: [
                    // Payment ID with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.indigo.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.indigo.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.payments,
                                size: 16, color: Colors.indigo.shade700),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                "Payment No #${payment.id.toString().padLeft(2, '0')}",
                                style: TextStyle(
                                  color: Colors.indigo.shade800,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Bill allocation status chip
                    Expanded(
                      child: FutureBuilder<List<int>>(
                        future: _getLinkedBillIds(payment),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.blue.shade200),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.hourglass_empty,
                                      size: 16, color: Colors.blue.shade700),
                                  const SizedBox(width: 6),
                                  Expanded(
                                    child: Text(
                                      "Loading...",
                                      style: TextStyle(
                                        color: Colors.blue.shade800,
                                        fontSize: 13,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          final billIds = snapshot.data ?? [];
                          IconData icon;
                          String text;
                          Color bgColor;
                          Color borderColor;
                          Color iconColor;
                          Color textColor;

                          if (billIds.isEmpty) {
                            icon = Icons.account_balance_wallet;
                            text = 'Credit';
                            bgColor = Colors.teal.shade50;
                            borderColor = Colors.teal.shade200;
                            iconColor = Colors.teal.shade700;
                            textColor = Colors.teal.shade800;
                          } else if (billIds.length == 1) {
                            icon = Icons.receipt;
                            text = 'Bill No #${billIds[0]}';
                            bgColor = Colors.blue.shade50;
                            borderColor = Colors.blue.shade200;
                            iconColor = Colors.blue.shade700;
                            textColor = Colors.blue.shade800;
                          } else {
                            icon = Icons.receipt_long;
                            text = '${billIds.length} Bills';
                            bgColor = Colors.blue.shade50;
                            borderColor = Colors.blue.shade200;
                            iconColor = Colors.blue.shade700;
                            textColor = Colors.blue.shade800;
                          }

                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: bgColor,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: borderColor),
                            ),
                            child: Row(
                              children: [
                                Icon(icon, size: 16, color: iconColor),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    text,
                                    style: TextStyle(
                                      color: textColor,
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Edit button
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AutoPaymentFormScreen(
                              customer: widget.customer,
                              existingPayment: payment,
                            ),
                          ),
                        ).then((result) {
                          if (result == true) {
                            refreshData();
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.cyan.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.cyan.shade200),
                        ),
                        child: Icon(Icons.edit,
                            color: Colors.cyan.shade700, size: 16),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),
                const Divider(height: 1, thickness: 1),
                const SizedBox(height: 12),

                // Row 2: Date, amount and delete
                Row(
                  children: [
                    // Date with icon
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 12, color: Colors.purple.shade700),
                            const SizedBox(width: 3),
                            Expanded(
                              child: Text(
                                formattedDate,
                                style: TextStyle(
                                  color: Colors.purple.shade800,
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Amount with icon
                    Expanded(
                      child: AmountChip(
                        amount: payment.amount,
                        showLabel: false,
                        customColor: payment.billId > 0
                            ? Colors.green.shade700
                            : Colors.indigo.shade700,
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Delete button
                    GestureDetector(
                      onTap: () => _confirmDeletePayment(context, payment),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Icon(Icons.delete,
                            color: Colors.red.shade700, size: 16),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _confirmDeletePayment(BuildContext context, Payment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.warning,
                        color: Colors.orange.shade700, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'This action cannot be undone.',
                        style: TextStyle(
                          color: Colors.orange.shade900,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text('Payment details:'),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.tag, color: Colors.blue.shade700, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Payment ID: ${payment.id}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.payments,
                            color: Colors.indigo.shade700, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Amount: Rs ${payment.amount.toStringAsFixed(2)}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            color: Colors.purple.shade700, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Date: ${DateFormat('dd MMM yyyy').format(payment.paymentDate)}',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.delete_forever, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'This action cannot be undone.',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deletePayment(payment);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  void _deletePayment(Payment payment) async {
    try {
      // Optimistic UI update - remove from list immediately
      setState(() {
        _payments.remove(payment);
      });

      // Use PaymentService instead of DatabaseService to properly handle bill status updates
      await PaymentService.deletePaymentAndUpdateBillStatus(payment);

      // Refresh data to ensure consistency
      refreshData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Payment deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Refresh data to restore correct state in case of error
        refreshData();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete payment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Extract linked bill IDs from a payment
  Future<List<int>> _getLinkedBillIds(Payment payment) async {
    final List<int> billIds = [];

    // Add the primary billId if it's valid
    if (payment.billId > 0) {
      billIds.add(payment.billId);
    }

    // Get payment allocations from database service
    try {
      final allocations =
          await DatabaseService.getPaymentAllocationsByPaymentId(payment.id);
      for (var allocation in allocations) {
        if (allocation.billId > 0) {
          final billId = allocation.billId;
          if (!billIds.contains(billId)) {
            billIds.add(billId);
          }
        }
      }
    } catch (e) {
      // Error handled silently
    }

    // Extract additional bill IDs from remarks
    final remarks = payment.remarks?.toLowerCase() ?? '';

    // Look for patterns like "Rs X for bill #Y"
    RegExp billRegex = RegExp(r'bill #(\d+)|bill (\d+)|bill.*?(\d+)');
    final matches = billRegex.allMatches(remarks);

    for (final match in matches) {
      // Try to get the bill ID from any capturing group
      String? billIdStr;
      for (int i = 1; i <= match.groupCount; i++) {
        if (match.group(i) != null) {
          billIdStr = match.group(i);
          break;
        }
      }

      if (billIdStr != null) {
        final billId = int.tryParse(billIdStr);
        if (billId != null && billId > 0 && !billIds.contains(billId)) {
          billIds.add(billId);
        }
      }
    }

    return billIds;
  }
}
