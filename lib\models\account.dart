import 'package:uuid/uuid.dart';

class Account {
  final String id;
  String name;
  String? description;
  DateTime createdAt;
  DateTime lastAccessed;

  Account({
    String? id,
    required this.name,
    this.description,
    DateTime? createdAt,
    DateTime? lastAccessed,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        lastAccessed = lastAccessed ?? DateTime.now();

  // Factory method to create an Account from a map
  factory Account.fromMap(Map<String, dynamic> map) {
    try {
      return Account(
        id: map['id'] as String?,
        name: map['name'] as String,
        description: map['description'] as String?,
        createdAt: map['createdAt'] != null
            ? DateTime.parse(map['createdAt'] as String)
            : null,
        lastAccessed: map['lastAccessed'] != null
            ? DateTime.parse(map['lastAccessed'] as String)
            : null,
      );
    } catch (e) {
      // Error parsing Account from map
      // Return a default account if parsing fails
      return Account(name: 'Default Account');
    }
  }

  // Convert Account to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }

  // Update last accessed time
  void updateLastAccessed() {
    lastAccessed = DateTime.now();
  }

  @override
  String toString() {
    return 'Account{id: $id, name: $name}';
  }
}
