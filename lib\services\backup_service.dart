import 'dart:io';
import 'dart:async';
import 'dart:convert';
// Import needed for the class but not directly used in code
// ignore: unused_import
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/settings_service.dart';
import 'package:tubewell_water_billing/services/background_backup_service.dart';
import 'package:tubewell_water_billing/services/account_service.dart'; // Added for account name
import 'permission_service.dart';

class BackupService with WidgetsBindingObserver {
  final Logger _logger = Logger();

  Timer? _scheduledBackupTimer;
  bool _isScheduledBackupEnabled = false;
  int _scheduledBackupInterval = 24; // Default to 24 hours
  static bool _isInitialized = false;

  BackupService() {
    _loadScheduledBackupSettings();
    _initializeAppLifecycleObserver();
  }

  /// Initialize app lifecycle observer to handle app state changes
  void _initializeAppLifecycleObserver() {
    if (!_isInitialized) {
      WidgetsBinding.instance.addObserver(this);
      _isInitialized = true;
      _logger.i('App lifecycle observer initialized');
    }
  }

  /// Handle app lifecycle changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _logger.i('App resumed - checking scheduled backup settings');
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _logger.i('App paused');
        break;
      case AppLifecycleState.detached:
        _logger.i('App detached');
        break;
      case AppLifecycleState.inactive:
        _logger.i('App inactive');
        break;
      case AppLifecycleState.hidden:
        _logger.i('App hidden');
        break;
    }
  }

  /// Handle app resume - restart timers if needed
  Future<void> _onAppResumed() async {
    try {
      await _loadScheduledBackupSettings();
      if (_isScheduledBackupEnabled) {
        _logger.i('Restarting foreground timer after app resume');
        _startScheduledBackup();
      }
    } catch (e) {
      _logger.e('Error handling app resume: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _scheduledBackupTimer?.cancel();
    if (_isInitialized) {
      WidgetsBinding.instance.removeObserver(this);
      _isInitialized = false;
    }
  }

  Future<void> _loadScheduledBackupSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _isScheduledBackupEnabled =
        prefs.getBool('scheduled_backup_enabled') ?? false;
    _scheduledBackupInterval = prefs.getInt('scheduled_backup_interval') ?? 24;

    if (_isScheduledBackupEnabled) {
      _startScheduledBackup();
    }
  }

  Future<void> _saveScheduledBackupSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('scheduled_backup_enabled', _isScheduledBackupEnabled);
    await prefs.setInt('scheduled_backup_interval', _scheduledBackupInterval);
  }

  void _startScheduledBackup() {
    _scheduledBackupTimer?.cancel();
    _scheduledBackupTimer = Timer.periodic(
      Duration(hours: _scheduledBackupInterval),
      (_) => _performScheduledBackup(),
    );
  }

  void _stopScheduledBackup() {
    _scheduledBackupTimer?.cancel();
    _scheduledBackupTimer = null;
  }

  Future<void> _performScheduledBackup() async {
    try {
      // For scheduled backups, we don't have a BuildContext, so we pass null
      // The method will check if storage permission is already granted
      final result = await backupToLocalDevice();
      final success = result['success'] as bool;

      if (success) {
        _logger.i('Scheduled backup completed successfully');
      } else {
        _logger
            .w('Scheduled backup failed - possibly due to missing permissions');
        // Here we could add code to show a notification to the user
        // about the failed backup and how to grant permissions
      }
    } catch (e) {
      _logger.e('Error during scheduled backup: $e');
    }
  }

  Future<void> setScheduledBackup({
    required bool enabled,
    required int intervalHours,
  }) async {
    _isScheduledBackupEnabled = enabled;
    _scheduledBackupInterval = intervalHours;

    await _saveScheduledBackupSettings();

    if (enabled) {
      // Start foreground timer and background service (placeholder)
      _startScheduledBackup();
      await _startBackgroundBackup(intervalHours);
    } else {
      // Stop foreground timer and background service (placeholder)
      _stopScheduledBackup();
      await _stopBackgroundBackup();
    }
  }

  /// Start background backup service using WorkManager (placeholder while disabled)
  Future<void> _startBackgroundBackup(int intervalHours) async {
    try {
      await BackgroundBackupService.schedulePeriodicBackup(
        intervalHours: intervalHours,
      );
      _logger.i('Background backup service started with $intervalHours hour interval (placeholder)');
    } catch (e) {
      _logger.e('Failed to start background backup service: $e');
      // Continue with foreground timer even if background service fails
    }
  }

  /// Stop background backup service (placeholder while disabled)
  Future<void> _stopBackgroundBackup() async {
    try {
      await BackgroundBackupService.cancelScheduledBackup();
      _logger.i('Background backup service stopped (placeholder)');
    } catch (e) {
      _logger.e('Failed to stop background backup service: $e');
    }
  }

  Map<String, dynamic> getScheduledBackupSettings() {
    return {
      'enabled': _isScheduledBackupEnabled,
      'interval': _scheduledBackupInterval,
    };
  }



  Future<Map<String, dynamic>> backupToLocalDevice(
      [BuildContext? context]) async {
    try {
      // If context is provided, use PermissionService to request permission
      if (context != null) {
        final permissionGranted =
            await PermissionService.requestStoragePermission(context);
        if (!permissionGranted) {
          _logger.e('Storage permission not granted');
          return {'success': false, 'error': 'Storage permission not granted'};
        }

        // Check if context is still mounted after the async operation
        if (!context.mounted) {
          _logger.e('Context no longer mounted after permission request');
          return {'success': false, 'error': 'Operation cancelled'};
        }
      } else {
        // Fallback to direct permission request when no context is provided
        // This is used for scheduled backups or background operations
        final status = await Permission.storage.status;
        if (!status.isGranted) {
          _logger.e(
              'Storage permission not granted and no context provided to request it');
          return {'success': false, 'error': 'Storage permission not granted'};
        }
      }

      // Create the backup file in temp directory
      final backupFile = await _createBackupFile();

      // Verify the backup file was created successfully
      if (!await backupFile.exists()) {
        _logger.e('Backup file was not created successfully');
        return {'success': false, 'error': 'Failed to create backup file'};
      }

      // Always use the Downloads folder for backups on Android
      Directory? downloadsDir;

      if (Platform.isAndroid) {
        // For Android, always use the standard Downloads directory
        // This is more reliable and easier for users to find
        downloadsDir = Directory('/storage/emulated/0/Download');

        // Check if the directory exists
        if (!await downloadsDir.exists()) {
          _logger.e('Could not access Downloads directory at ${downloadsDir.path}');

          // Try to create the directory if it doesn't exist
          try {
            await downloadsDir.create(recursive: true);
            _logger.i('Created Downloads directory');
          } catch (e) {
            _logger.e('Failed to create Downloads directory: $e');

            // Fallback to app-specific directory
            final directory = await getExternalStorageDirectory();
            if (directory == null) {
              _logger.e('Could not access external storage');
              return {'success': false, 'error': 'Could not access Downloads or external storage'};
            }
            downloadsDir = directory;
            _logger.i('Using fallback directory: ${downloadsDir.path}');
          }
        }
      } else {
        // Fallback to app-specific directory for other platforms
        final directory = await getExternalStorageDirectory();
        if (directory == null) {
          _logger.e('Could not access external storage');
          return {'success': false, 'error': 'Could not access external storage'};
        }
        downloadsDir = directory;
      }

      // Create a tubewell_backups directory inside Downloads if it doesn't exist
      Directory backupDir = Directory('${downloadsDir.path}/tubewell_backups');
      _logger.i('Backup directory path: ${backupDir.path}');

      if (!await backupDir.exists()) {
        try {
          await backupDir.create(recursive: true);
          _logger.i('Created tubewell_backups directory');
        } catch (e) {
          _logger.e('Failed to create tubewell_backups directory: $e');

          // If we can't create the subdirectory, use the Downloads directory directly
          _logger.i('Using Downloads directory directly');
          backupDir = downloadsDir;
        }
      }

      // Generate backup filename with timestamp
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupPath = '${backupDir.path}/tubewell_backup_$timestamp.db';

      // Store account name in the backup before copying
      await _storeAccountNameInBackup(backupFile.path);

      // Copy the backup file to the final location
      await backupFile.copy(backupPath);

      // Delete the temporary file
      await backupFile.delete();

      // Save backup information
      final accountId = DatabaseService.getCurrentAccountId();
      final now = DateTime.now();
      final formattedDate = DateFormat('MMM d, yyyy HH:mm').format(now);
      await SettingsService.saveLastBackupDate(formattedDate,
          accountId: accountId);

      return {'success': true, 'path': backupPath};
    } catch (e) {
      _logger.e('Error backing up to local device: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<File> _createBackupFile() async {
    final tempDir = await getTemporaryDirectory();
    final backupFile = File('${tempDir.path}/temp_backup.db');

    try {
      // Make sure the temp directory exists
      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      // Create an empty file if it doesn't exist
      if (!await backupFile.exists()) {
        await backupFile.create(recursive: true);
      } else {
        // If file exists, delete it first to ensure a clean copy
        await backupFile.delete();
        await backupFile.create(recursive: true);
      }

      // Get the SQLite database path
      final dbPath = await DatabaseService.getDatabasePath();

      // If we have a valid database path, copy the database file
      if (dbPath.isNotEmpty) {
        final dbFile = File(dbPath);
        if (await dbFile.exists()) {
          // Use more efficient file operations
          try {
            // Copy the database file to the temp location using byte transfer
            // This is more efficient than the standard copy method
            final bytes = await dbFile.readAsBytes();
            await backupFile.writeAsBytes(bytes, flush: true);

            _logger
                .i('Database file copied to temp location: ${backupFile.path}');
            return backupFile;
          } catch (e) {
            _logger.w('Error during efficient file copy: $e, falling back to standard copy');
            // Fallback to standard copy if the efficient method fails
            await dbFile.copy(backupFile.path);
            _logger
                .i('Database file copied to temp location using standard method: ${backupFile.path}');
            return backupFile;
          }
        }
      }

      // If we couldn't copy the database file, create a simple backup with metadata
      final timestamp = DateTime.now().toIso8601String();
      final currentAccount = AccountService.currentAccount;
      final metadata = {
        'timestamp': timestamp,
        'type': 'backup',
        'version': '1.0',
        'database': 'sqlite',
        'accountId': currentAccount?.id, // Include current account ID
        'accountName': currentAccount?.name, // Include current account name
      };

      // Write metadata to the file
      await backupFile
          .writeAsString('TUBEWELL_BACKUP\n${DateTime.now()}\n${jsonEncode(metadata)}'); // Encode metadata as JSON
      _logger.i('Created backup file with metadata at: ${backupFile.path}');

      return backupFile;
    } catch (e) {
      _logger.e('Error creating backup file: $e');

      // Create a minimal backup file as a last resort
      try {
        await backupFile
            .writeAsString('Emergency backup created at ${DateTime.now()}');
        return backupFile;
      } catch (fallbackError) {
        _logger
            .e('Failed to create even a minimal backup file: $fallbackError');
        throw Exception('Could not create backup file: $fallbackError');
      }
    }
  }

  /// Store the current account name in the backup database
  Future<void> _storeAccountNameInBackup(String backupPath) async {
    Database? backupDb;
    try {
      _logger.i('Attempting to store account name in backup: $backupPath');

      // Open the backup database
      backupDb = await openDatabase(backupPath);

      // Get current account information
      final currentAccount = AccountService.currentAccount;
      _logger.i('Current account: ${currentAccount?.name} (${currentAccount?.id})');

      if (currentAccount != null) {
        // Store account name in settings table
        await backupDb.rawInsert(
          'INSERT OR REPLACE INTO settings (key, value, accountId) VALUES (?, ?, ?)',
          ['accountName', currentAccount.name, currentAccount.id]
        );
        _logger.i('Stored account name "${currentAccount.name}" in backup');

        // Verify the storage by reading it back
        final verification = await backupDb.rawQuery(
          'SELECT * FROM settings WHERE key = ?',
          ['accountName']
        );
        _logger.i('Verification - account name entries in backup: $verification');
      } else {
        _logger.w('No current account found, cannot store account name in backup');
      }
    } catch (e) {
      _logger.e('Error storing account name in backup: $e');
      // Don't throw - this is not critical for the backup process
    } finally {
      if (backupDb != null) {
        try {
          await backupDb.close();
        } catch (e) {
          _logger.e('Error closing backup database: $e');
        }
      }
    }
  }
}


