import 'package:flutter/material.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/services/database_service.dart';
import 'package:tubewell_water_billing/services/currency_service.dart';

class CustomerSummaryTab extends StatefulWidget {
  final Customer customer;
  final VoidCallback onDataChanged;
  final double totalBilledAmount;
  final double totalPaidAmount;
  final double netBalance;
  final bool isLoadingSummary;
  final VoidCallback onReminderPressed;
  final VoidCallback onRefreshPressed;

  const CustomerSummaryTab({
    super.key,
    required this.customer,
    required this.onDataChanged,
    required this.totalBilledAmount,
    required this.totalPaidAmount,
    required this.netBalance,
    required this.isLoadingSummary,
    required this.onReminderPressed,
    required this.onRefreshPressed,
  });

  @override
  State<CustomerSummaryTab> createState() => CustomerSummaryTabState();
}

class CustomerSummaryTabState extends State<CustomerSummaryTab>
    with AutomaticKeepAliveClientMixin {
  Map<String, dynamic> _discountSummary = {};
  bool _isLoadingDiscounts = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadDiscountSummary();
  }

  // Public method to force a refresh from parent
  void refreshData() {
    if (mounted) {
      _loadDiscountSummary();
    }
  }

  Future<void> _loadDiscountSummary() async {
    if (!mounted) return;

    setState(() {
      _isLoadingDiscounts = true;
    });

    try {
      // Get all bills for this customer to calculate discount metrics
      final bills = await DatabaseService.getBillsByCustomer(widget.customer.id);

      double totalAmountDiscount = 0.0;
      double totalTimeDiscount = 0.0; // in minutes
      int billsWithAmountDiscount = 0;
      int billsWithTimeDiscount = 0;

      for (final bill in bills) {
        if (bill.discountAmount != null && bill.discountAmount! > 0) {
          totalAmountDiscount += bill.discountAmount!;
          billsWithAmountDiscount++;
        }
        if (bill.discountTime != null && bill.discountTime! > 0) {
          totalTimeDiscount += bill.discountTime!;
          billsWithTimeDiscount++;
        }
      }

      final summary = {
        'totalAmountDiscount': totalAmountDiscount,
        'totalTimeDiscount': totalTimeDiscount,
        'billsWithAmountDiscount': billsWithAmountDiscount,
        'billsWithTimeDiscount': billsWithTimeDiscount,
        'totalBills': bills.length,
      };

      if (mounted) {
        setState(() {
          _discountSummary = summary;
          _isLoadingDiscounts = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _discountSummary = {};
          _isLoadingDiscounts = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefreshPressed();
        await _loadDiscountSummary();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            // Customer Financial Summary Card (matches overall tab style)
            _buildFinancialSummaryCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(8, 8, 8, 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header (matching summary card style)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      'Financial Summary',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade900,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Send reminder button
                      if (widget.netBalance < 0)
                        GestureDetector(
                          onTap: widget.onReminderPressed,
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: Colors.purple.shade50,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.purple.shade200),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.notifications_active,
                                    size: 16, color: Colors.purple.shade700),
                                const SizedBox(width: 4),
                                Text(
                                  'Remind',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.purple.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      // Refresh button
                      GestureDetector(
                        onTap: widget.onRefreshPressed,
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Icon(Icons.refresh,
                              size: 16, color: Colors.blue.shade700),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Bill count
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '${_discountSummary['totalBills'] ?? 0} bills',
              style: TextStyle(
                color: Colors.blue.shade700,
                fontSize: 14,
              ),
            ),
          ),

          const SizedBox(height: 16),

          if (widget.isLoadingSummary)
            const Center(child: CircularProgressIndicator())
          else
            Column(
              children: [
                // Total Billed (large prominent display like summary card)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade300),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long,
                            color: Colors.blue.shade700,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Total Amount',
                            style: TextStyle(
                              color: Colors.blue.shade800,
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          CurrencyService.formatCurrency(widget.totalBilledAmount),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade800,
                            fontSize: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 12),

                // Paid/Outstanding split (like summary card)
                Row(
                  children: [
                    // Total Paid
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green.shade700,
                                  size: 18,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'Paid',
                                  style: TextStyle(
                                    color: Colors.green.shade800,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                CurrencyService.formatCurrency(widget.totalPaidAmount),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade800,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Outstanding (Net Balance if negative)
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: widget.netBalance < 0 ? Colors.red.shade50 : Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: widget.netBalance < 0 ? Colors.red.shade200 : Colors.grey.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  widget.netBalance < 0 ? Icons.attach_money : Icons.account_balance_wallet,
                                  color: widget.netBalance < 0 ? Colors.red.shade700 : Colors.grey.shade700,
                                  size: 18,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  widget.netBalance < 0 ? 'Unpaid' : 'Credit',
                                  style: TextStyle(
                                    color: widget.netBalance < 0 ? Colors.red.shade800 : Colors.grey.shade800,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                CurrencyService.formatCurrency(widget.netBalance.abs()),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: widget.netBalance < 0 ? Colors.red.shade800 : Colors.grey.shade800,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Discount Metrics Section
                _buildDiscountMetricsSection(),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildDiscountMetricsSection() {
    final double totalAmountDiscount = _discountSummary['totalAmountDiscount']?.toDouble() ?? 0.0;
    final double totalTimeDiscount = _discountSummary['totalTimeDiscount']?.toDouble() ?? 0.0;
    final int billsWithAmountDiscount = _discountSummary['billsWithAmountDiscount']?.toInt() ?? 0;
    final int billsWithTimeDiscount = _discountSummary['billsWithTimeDiscount']?.toInt() ?? 0;

    // Convert time discount to hours and minutes for display
    final int totalTimeHours = (totalTimeDiscount / 60).floor();
    final int totalTimeMinutes = (totalTimeDiscount % 60).round();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.pink.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.pink.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Icon(
                Icons.discount,
                color: Colors.pink.shade700,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Discount Metrics',
                style: TextStyle(
                  color: Colors.pink.shade800,
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          if (_isLoadingDiscounts)
            const Center(child: CircularProgressIndicator())
          else if (totalAmountDiscount == 0 && totalTimeDiscount == 0)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.info_outline, color: Colors.grey.shade600, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'No discounts given to this customer',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
                ],
              ),
            )
          else
            // Discount metrics in a grid (like overall tab)
            Column(
              children: [
                // First row: Amount and Time discounts
                Row(
                  children: [
                    // Amount Discount
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.cyan.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.cyan.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.money_off,
                                  color: Colors.cyan.shade700,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Amount Discount',
                                    style: TextStyle(
                                      color: Colors.cyan.shade800,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 13,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              CurrencyService.formatCurrency(totalAmountDiscount),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.cyan.shade800,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              '$billsWithAmountDiscount bills',
                              style: TextStyle(
                                color: Colors.cyan.shade600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Time Discount
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.indigo.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.indigo.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.access_time,
                                  color: Colors.indigo.shade700,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Time Discount',
                                    style: TextStyle(
                                      color: Colors.indigo.shade800,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 13,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              totalTimeHours > 0
                                  ? '${totalTimeHours}h ${totalTimeMinutes}m'
                                  : '${totalTimeMinutes}m',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.indigo.shade800,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              '$billsWithTimeDiscount bills',
                              style: TextStyle(
                                color: Colors.indigo.shade600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Second row: Equivalent conversions
                Row(
                  children: [
                    // Equivalent Time from Amount Discount
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.teal.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.teal.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.swap_horiz,
                                  color: Colors.teal.shade700,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Total Time',
                                    style: TextStyle(
                                      color: Colors.teal.shade800,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 13,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            FutureBuilder<String>(
                              future: _calculateEquivalentTime(totalAmountDiscount),
                              builder: (context, snapshot) {
                                return Text(
                                  snapshot.data ?? '...',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.teal.shade800,
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center,
                                );
                              },
                            ),
                            Text(
                              'from amount',
                              style: TextStyle(
                                color: Colors.teal.shade600,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Equivalent Amount from Time Discount
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.deepPurple.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.deepPurple.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.swap_vert,
                                  color: Colors.deepPurple.shade700,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Total Amount',
                                    style: TextStyle(
                                      color: Colors.deepPurple.shade800,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 13,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            FutureBuilder<String>(
                              future: _calculateEquivalentAmount(totalTimeDiscount),
                              builder: (context, snapshot) {
                                return Text(
                                  snapshot.data ?? '...',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.deepPurple.shade800,
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center,
                                );
                              },
                            ),
                            Text(
                              'from time',
                              style: TextStyle(
                                color: Colors.deepPurple.shade600,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
        ],
      ),
    );
  }

  String _formatTimeDiscount(double timeInMinutes) {
    if (timeInMinutes <= 0) return '0m';

    final int hours = (timeInMinutes / 60).floor();
    final int minutes = (timeInMinutes % 60).round();

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  Future<String> _calculateEquivalentTime(double amountDiscount) async {
    if (amountDiscount <= 0) return '0m';

    try {
      final double hourlyRate = await CurrencyService.getDefaultHourlyRate();
      if (hourlyRate <= 0) return 'N/A';

      final double equivalentTimeMinutes = (amountDiscount / hourlyRate) * 60;
      return _formatTimeDiscount(equivalentTimeMinutes);
    } catch (e) {
      return 'N/A';
    }
  }

  Future<String> _calculateEquivalentAmount(double timeInMinutes) async {
    if (timeInMinutes <= 0) return CurrencyService.formatCurrency(0.0);

    try {
      final double hourlyRate = await CurrencyService.getDefaultHourlyRate();
      if (hourlyRate <= 0) return 'N/A';

      final double equivalentAmount = (timeInMinutes / 60) * hourlyRate;
      return CurrencyService.formatCurrency(equivalentAmount);
    } catch (e) {
      return 'N/A';
    }
  }
}
