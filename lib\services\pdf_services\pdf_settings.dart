import 'package:pdf/pdf.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';

// Import the enums directly
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart'
    show TemplateType, HeaderStyle, FooterStyle, ContentStyle;

/// Settings for PDF generation
class PdfSettings {
  /// Company information
  final String companyName;
  final String? companyAddress;
  final String? companyPhone;
  final String? companyEmail;
  final String? companyWebsite;

  /// Template settings
  final TemplateType templateType;
  final HeaderStyle headerStyle;
  final FooterStyle footerStyle;
  final ContentStyle contentStyle;

  /// Color settings
  final PdfColor primaryColor;
  final PdfColor accentColor;
  final PdfColor textColor;

  /// Display settings
  final bool showLogo;
  final bool showPageNumbers;
  final bool showTimestamp;
  final bool showWatermark;
  final String? watermarkText;

  /// Additional settings
  final Map<String, dynamic>? additionalSettings;

  /// Default constructor
  const PdfSettings({
    this.companyName = 'Tubewell Water Billing',
    this.companyAddress,
    this.companyPhone,
    this.companyEmail,
    this.companyWebsite,
    this.templateType = TemplateType.standard,
    this.headerStyle = HeaderStyle.standard,
    this.footerStyle = FooterStyle.standard,
    this.contentStyle = ContentStyle.standard,
    this.primaryColor = const PdfColor(0.1, 0.46, 0.82), // #1976D2
    this.accentColor = const PdfColor(0.18, 0.49, 0.2), // #2E7D32
    this.textColor = const PdfColor(0.15, 0.2, 0.22), // #263238
    this.showLogo = true,
    this.showPageNumbers = true,
    this.showTimestamp = true,
    this.showWatermark = false,
    this.watermarkText,
    this.additionalSettings,
  });

  /// Create a copy with modified values
  PdfSettings copyWith({
    String? companyName,
    String? companyAddress,
    String? companyPhone,
    String? companyEmail,
    String? companyWebsite,
    TemplateType? templateType,
    HeaderStyle? headerStyle,
    FooterStyle? footerStyle,
    ContentStyle? contentStyle,
    PdfColor? primaryColor,
    PdfColor? accentColor,
    PdfColor? textColor,
    bool? showLogo,
    bool? showPageNumbers,
    bool? showTimestamp,
    bool? showWatermark,
    String? watermarkText,
    Map<String, dynamic>? additionalSettings,
  }) {
    return PdfSettings(
      companyName: companyName ?? this.companyName,
      companyAddress: companyAddress ?? this.companyAddress,
      companyPhone: companyPhone ?? this.companyPhone,
      companyEmail: companyEmail ?? this.companyEmail,
      companyWebsite: companyWebsite ?? this.companyWebsite,
      templateType: templateType ?? this.templateType,
      headerStyle: headerStyle ?? this.headerStyle,
      footerStyle: footerStyle ?? this.footerStyle,
      contentStyle: contentStyle ?? this.contentStyle,
      primaryColor: primaryColor ?? this.primaryColor,
      accentColor: accentColor ?? this.accentColor,
      textColor: textColor ?? this.textColor,
      showLogo: showLogo ?? this.showLogo,
      showPageNumbers: showPageNumbers ?? this.showPageNumbers,
      showTimestamp: showTimestamp ?? this.showTimestamp,
      showWatermark: showWatermark ?? this.showWatermark,
      watermarkText: watermarkText ?? this.watermarkText,
      additionalSettings: additionalSettings ?? this.additionalSettings,
    );
  }

  /// Get header data for template
  Map<String, dynamic> getHeaderData() {
    return {
      'companyName': companyName,
      'address': companyAddress ?? '',
      'phone': companyPhone ?? '',
      'email': companyEmail ?? '',
      'website': companyWebsite ?? '',
      ...?additionalSettings?['headerData'],
    };
  }

  /// Get footer data for template
  Map<String, dynamic> getFooterData() {
    return {
      'companyName': companyName,
      'customText': additionalSettings?['footerText'] ?? '',
      ...?additionalSettings?['footerData'],
    };
  }

  /// Standard invoice template settings
  static PdfSettings invoice() {
    return const PdfSettings(
      templateType: TemplateType.invoice,
      headerStyle: HeaderStyle.detailed,
      footerStyle: FooterStyle.detailed,
      contentStyle: ContentStyle.standard,
      primaryColor: PdfColor(0.1, 0.46, 0.82), // #1976D2
      accentColor: PdfColor(0.18, 0.49, 0.2), // #2E7D32
    );
  }

  /// Standard report template settings
  static PdfSettings report() {
    return const PdfSettings(
      templateType: TemplateType.report,
      headerStyle: HeaderStyle.standard,
      footerStyle: FooterStyle.standard,
      contentStyle: ContentStyle.standard,
      primaryColor: PdfColor(0.18, 0.49, 0.2), // #2E7D32
      accentColor: PdfColor(0.1, 0.46, 0.82), // #1976D2
    );
  }

  /// Standard statement template settings
  static PdfSettings statement() {
    return const PdfSettings(
      templateType: TemplateType.statement,
      headerStyle: HeaderStyle.compact,
      footerStyle: FooterStyle.compact,
      contentStyle: ContentStyle.standard,
      primaryColor: PdfColor(0.0, 0.47, 0.42), // #00796B
      accentColor: PdfColor(0.1, 0.46, 0.82), // #1976D2
    );
  }

  /// Minimal template settings
  static PdfSettings minimal() {
    return const PdfSettings(
      templateType: TemplateType.standard,
      headerStyle: HeaderStyle.minimal,
      footerStyle: FooterStyle.minimal,
      contentStyle: ContentStyle.standard,
      primaryColor: PdfColor(0.15, 0.2, 0.22), // #263238
      accentColor: PdfColor(0.47, 0.56, 0.61), // #78909C
      showLogo: false,
    );
  }

  /// Two-column template settings
  static PdfSettings twoColumn() {
    return const PdfSettings(
      templateType: TemplateType.standard,
      headerStyle: HeaderStyle.standard,
      footerStyle: FooterStyle.standard,
      contentStyle: ContentStyle.twoColumn,
      primaryColor: PdfColor(0.1, 0.46, 0.82), // #1976D2
      accentColor: PdfColor(0.18, 0.49, 0.2), // #2E7D32
    );
  }

  /// Grid layout template settings
  static PdfSettings grid() {
    return const PdfSettings(
      templateType: TemplateType.standard,
      headerStyle: HeaderStyle.standard,
      footerStyle: FooterStyle.standard,
      contentStyle: ContentStyle.grid,
      primaryColor: PdfColor(0.1, 0.46, 0.82), // #1976D2
      accentColor: PdfColor(0.18, 0.49, 0.2), // #2E7D32
    );
  }

  /// Modern template settings with enhanced styling
  static PdfSettings modern() {
    return const PdfSettings(
      templateType: TemplateType.modern,
      headerStyle: HeaderStyle.modern,
      footerStyle: FooterStyle.modern,
      contentStyle: ContentStyle.modern,
      primaryColor: PdfColor(0.15, 0.39, 0.96), // #2563EB - Modern blue
      accentColor: PdfColor(0.06, 0.73, 0.51), // #10B981 - Modern green
      textColor: PdfColor(0.12, 0.16, 0.23), // #1E293B - Slate 800
      showLogo: true,
      showPageNumbers: true,
      showTimestamp: true,
    );
  }

  /// Modern invoice template settings
  static PdfSettings modernInvoice() {
    return const PdfSettings(
      templateType: TemplateType.invoice,
      headerStyle: HeaderStyle.modern,
      footerStyle: FooterStyle.modern,
      contentStyle: ContentStyle.cards,
      primaryColor: PdfColor(0.15, 0.39, 0.96), // #2563EB - Modern blue
      accentColor: PdfColor(0.06, 0.73, 0.51), // #10B981 - Modern green
      textColor: PdfColor(0.12, 0.16, 0.23), // #1E293B - Slate 800
      additionalSettings: {
        'footerText': 'Thank you for your business!',
      },
    );
  }
}
