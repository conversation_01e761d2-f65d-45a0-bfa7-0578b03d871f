import 'dart:io';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_base_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_settings.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_template_service.dart';
import 'package:tubewell_water_billing/services/pdf_services/pdf_utility_service.dart';

class PdfExpensesService {
  static Future<File> generateExpensesPdf({
    required List<Expense> expenses,
    Map<String, double>? summary,
    Map<String, double>? categoryTotals,
    Map<String, dynamic>? filters,
    PdfSettings? settings,
  }) async {
    try {
      // Use provided settings or create default modern settings
      final pdfSettings = settings ?? PdfSettings.modern().copyWith(
        primaryColor: PdfColor.fromHex('#2E7D32'), // Green
        accentColor: PdfColor.fromHex('#1565C0'), // Blue
        additionalSettings: {
          'footerText': 'Expenses Report',
        },
      );

      // Define colors from settings
      final primaryColor = pdfSettings.primaryColor;
      final accentColor = pdfSettings.accentColor;
      final textColor = pdfSettings.textColor;
      final lightGreen = PdfBaseService.lightGreen;
      final lightBlue = PdfBaseService.lightBlue;

      // Access fonts from PdfBaseService
      final boldFont = PdfBaseService.boldFont;
      final regularFont = PdfBaseService.regularFont;
      final italicFont = PdfBaseService.italicFont;

      // Create a PDF document with template
      final pdf = await PdfTemplateService.createPdfWithTemplate(
        title: 'EXPENSES REPORT',
        subtitle: 'Expense Records',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        watermarkText: pdfSettings.showWatermark ? (pdfSettings.watermarkText ?? 'COPY') : null,
        headerData: pdfSettings.getHeaderData(),
        footerData: pdfSettings.getFooterData(),
      );

      expenses.sort((a, b) => b.date.compareTo(a.date));
      Map<String, double> summaryData = summary ?? {'totalAmount': 0.0, 'count': 0.0};
      if (summary == null) {
        for (var expense in expenses) {
          summaryData['totalAmount'] = (summaryData['totalAmount'] ?? 0) + expense.amount;
          summaryData['count'] = (summaryData['count'] ?? 0) + 1;
        }
      }
      Map<String, double> categoryData = categoryTotals ?? {};
      if (categoryTotals == null) {
        for (var expense in expenses) {
          categoryData[expense.category] = (categoryData[expense.category] ?? 0) + expense.amount;
        }
      }

      // Create content widgets
      final List<pw.Widget> contentWidgets = [
            pw.SizedBox(height: 20),

            // Report Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: lightGreen,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('EXPENSE SUMMARY',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 14,
                        color: primaryColor,
                      )),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Expenses:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('${summaryData['count']?.toInt() ?? 0}',
                                style: pw.TextStyle(font: boldFont, fontSize: 16)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('Total Amount:',
                                style: pw.TextStyle(font: boldFont, fontSize: 11)),
                            pw.Text('Rs. ${(summaryData['totalAmount'] ?? 0).toStringAsFixed(2)}',
                                style: pw.TextStyle(
                                    font: boldFont,
                                    fontSize: 16,
                                    color: primaryColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (filters != null && filters['category'] != null) ...[
                    pw.SizedBox(height: 10),
                    pw.Text('Category Filter: ${filters['category']}',
                        style: pw.TextStyle(font: boldFont, fontSize: 12)),
                  ],
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // Category breakdown
            if (categoryData.isNotEmpty) ...[
              pw.Text('EXPENSE CATEGORIES',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: primaryColor,
                  )),
              pw.Divider(color: primaryColor),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(
                  color: PdfColor.fromHex('#CCCCCC'),
                  width: 0.5,
                ),
                columnWidths: {
                  0: const pw.FlexColumnWidth(3), // Category
                  1: const pw.FlexColumnWidth(1), // Amount
                  2: const pw.FlexColumnWidth(1), // Percentage
                },
                children: [
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      color: lightGreen,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Category',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Amount',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('% of Total',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                    ],
                  ),
                  ...categoryData.entries.map((entry) {
                    final percentage = summaryData['totalAmount']! > 0
                        ? (entry.value / summaryData['totalAmount']! * 100)
                        : 0.0;
                    return pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(entry.key,
                              style: pw.TextStyle(
                                  font: regularFont, fontSize: 10)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text('Rs. ${entry.value.toStringAsFixed(2)}',
                              style: pw.TextStyle(font: boldFont, fontSize: 10)),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text('${percentage.toStringAsFixed(1)}%',
                              style: pw.TextStyle(
                                  font: regularFont, fontSize: 10)),
                        ),
                      ],
                    );
                  }),
                ],
              ),
              pw.SizedBox(height: 20),
            ],

            // Expenses Table
            pw.Text('EXPENSE DETAILS',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 14,
                  color: primaryColor,
                )),
            pw.Divider(color: primaryColor),
            if (expenses.isEmpty)
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: lightBlue,
                  borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                ),
                child: pw.Center(
                  child: pw.Text('No expenses found for the selected criteria',
                      style: pw.TextStyle(font: italicFont, fontSize: 12)),
                ),
              )
            else
              pw.Table(
                border: pw.TableBorder.all(
                  color: PdfColor.fromHex('#CCCCCC'),
                  width: 0.5,
                ),
                columnWidths: {
                  0: const pw.FixedColumnWidth(80), // Date
                  1: const pw.FlexColumnWidth(2), // Description
                  2: const pw.FixedColumnWidth(70), // Category
                  3: const pw.FixedColumnWidth(60), // Method
                  4: const pw.FixedColumnWidth(60), // Amount
                },
                children: [
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      color: lightGreen,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Date',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Description',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Category',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Method',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text('Amount',
                            style: pw.TextStyle(font: boldFont, fontSize: 10)),
                      ),
                    ],
                  ),
                  ...expenses.map((expense) => pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(5),
                            child: pw.Text(
                                DateFormat('dd/MM/yyyy').format(expense.date),
                                style: pw.TextStyle(
                                    font: regularFont, fontSize: 9)),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(5),
                            child: pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                pw.Text(expense.description,
                                    style: pw.TextStyle(
                                        font: regularFont, fontSize: 9)),
                                if (expense.remarks != null && expense.remarks!.isNotEmpty)
                                  pw.Text(expense.remarks!,
                                      style: pw.TextStyle(
                                          font: italicFont, fontSize: 8),
                                      maxLines: 2),
                              ],
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(5),
                            child: pw.Text(expense.category,
                                style: pw.TextStyle(
                                    font: regularFont, fontSize: 9)),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(5),
                            child: pw.Text(expense.paymentMethod ?? 'N/A',
                                style: pw.TextStyle(
                                    font: regularFont, fontSize: 9)),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(5),
                            child: pw.Text('Rs. ${expense.amount.toStringAsFixed(2)}',
                                style: pw.TextStyle(font: boldFont, fontSize: 9)),
                          ),
                        ],
                      )),
                ],
              ),

            // No footer needed as it's handled by the template
      ];

      // Add page with template using settings
      await PdfTemplateService.addPageWithTemplate(
        pdf: pdf,
        contentWidgets: PdfTemplateService.createContentSection(
          contentStyle: pdfSettings.contentStyle,
          contentWidgets: contentWidgets,
          primaryColor: primaryColor,
          accentColor: accentColor,
          textColor: textColor,
        ),
        title: 'EXPENSES REPORT',
        subtitle: 'Expense Records',
        templateType: pdfSettings.templateType,
        headerStyle: pdfSettings.headerStyle,
        footerStyle: pdfSettings.footerStyle,
        contentStyle: pdfSettings.contentStyle,
        primaryColor: primaryColor,
        accentColor: accentColor,
        textColor: textColor,
        includePageNumbers: pdfSettings.showPageNumbers,
        includeTimestamp: pdfSettings.showTimestamp,
        headerData: {
          ...pdfSettings.getHeaderData(),
          'period': filters != null && (filters['startDate'] != null || filters['endDate'] != null)
              ? 'Period: ${filters['startDate'] != null ? DateFormat('dd/MM/yyyy').format(filters['startDate']) : 'Start'} - ${filters['endDate'] != null ? DateFormat('dd/MM/yyyy').format(filters['endDate']) : 'End'}'
              : null,
        },
        footerData: pdfSettings.getFooterData(),
      );

      // Save the PDF file
      final fileName = 'expenses_${DateFormat('yyyyMMdd').format(DateTime.now())}.pdf';
      return await PdfUtilityService.savePdfToTemp(pdf, fileName);
    } catch (e) {
      rethrow;
    }
  }
}
